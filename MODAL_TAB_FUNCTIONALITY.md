# Modal Tab Functionality

This document describes the new tab functionality added to the system-wide modal component.

## Overview

The modal now supports multiple tabs with the following behavior:

**Key Feature:** Content always opens in tabs by default. When the modal is already open, new content will **never** overwrite existing tabs - it will always create a new tab instead. This prevents accidental loss of work and allows users to keep multiple pieces of content open simultaneously.

### Tab Management Rules
1. **When modal is closed/not visible:** Opening content creates a single tab and displays it
2. **When modal is already open:** Opening new content **always** creates a new tab and automatically switches to it (never overwrites existing tabs)
3. **Pin functionality:** Each tab has a pin icon that:
   - Shows as unpinned by default
   - When clicked, toggles to pinned state (visual indicator change)
   - Serves as a visual indicator for important tabs
   - All tabs persist until manually closed regardless of pin status

## Technical Implementation

**Architecture:** Server-side processing with HTMX as primary approach, minimal Alpine.js for UI state.

### Files Modified/Created

#### Core Components
- `system/components/edges/layout-main.edge.php` - Minimal Alpine.js (only modal show/hide)
- `system/components/edges/component-modal.edge.php` - Server-rendered tab container
- `system/components/edges/layout-head.edge.php` - Added modal-tab-handler.js script

#### New Files
- `system/classes/ModalTabState.php` - **Server-side tab state management** (session-based)
- `system/api/modal_tabs.api.php` - **HTMX endpoints for all tab operations**
- `system/components/edges/modal-with-tabs.edge.php` - Server-rendered tab UI
- `resources/components/js/modal-tab-handler.js` - Minimal HTMX integration (75 lines)
- `system/api/modal_tab_demo.api.php` - Demo API endpoints
- `system/views/modal_tab_demo.edge.php` - Demo page
- `add_modal_tab_demo_navigation.sql` - Navigation entry for demo

## Usage for Developers

### Making Buttons Create Tabs

**Simple approach:** Just add `data-tab-title` attribute to any HTMX button:

```html
<x-forms-button
    type="button"
    label="Your Button"
    hx-get="your-endpoint"
    hx-target="#modal_body"
    data-tab-title="Your Tab Title"
/>
```

**Key Benefits:**
- No Alpine.js click handlers needed
- No client-side state management
- Automatic tab creation via server-side processing
- Works with existing HTMX buttons by adding one attribute

### Server-Side Tab Management

Use the `ModalTabState` class for all tab operations:

```php
// Create a new tab
$tabId = ModalTabState::addTab('Tab Title', $content, $url);

// Switch to a tab
ModalTabState::switchTab($tabId);

// Close a tab
ModalTabState::closeTab($tabId);

// Toggle pin status
ModalTabState::togglePin($tabId);

// Update current tab
ModalTabState::updateCurrentTab($content, $title);

// Render complete modal
echo ModalTabState::renderModal();
```

### HTMX Endpoints

All tab operations are handled via HTMX endpoints:

- `POST /api/modal_tabs/add_tab` - Create new tab
- `POST /api/modal_tabs/switch_tab` - Switch to tab
- `POST /api/modal_tabs/close_tab` - Close tab
- `POST /api/modal_tabs/toggle_pin` - Toggle pin status
- `POST /api/modal_tabs/close_modal` - Close all tabs
- `POST /api/modal_tabs/load_content` - Load content into new tab

## User Features

### Keyboard Shortcuts
- `Ctrl+W` - Close current tab
- `Ctrl+1-9` - Switch to tab by number
- `←/→` - Navigate tabs when tab button is focused
- `Esc` - Close entire modal

### Tab Controls
- **Tab Button** - Click to switch to that tab
- **Pin Icon** - Click to pin/unpin tab (pinned tabs stay open when new content loads)
- **Close Button (X)** - Close individual tab
- **Global Close (X)** - Close entire modal and all tabs

## Examples

### Basic Tab Creation
```html
<button 
    hx-get="/api/demo/content"
    hx-target="#modal_body"
    data-tab-title="Demo Content"
    @click="if (!showModal) { showModal = true; }">
    Open Demo Tab
</button>
```

### Programmatic Tab Creation (Alpine.js)
```html
<button @click="
    $dispatch('modal-add-tab', {
        title: 'Custom Tab',
        content: '<div class=\'p-6\'><h2>Custom Content</h2></div>',
        url: ''
    });
">Create Custom Tab</button>
```

### Form in Tab
Forms work normally within tabs. Use standard HTMX attributes for form submission and content updates.

## Migration from Legacy Modal Usage

Existing modal usage will continue to work with these changes:

1. **No `data-tab-title`** - Content loads in legacy mode (single content area)
2. **With `data-tab-title`** - Content creates tabs automatically
3. **Mixed usage** - Legacy and tab content can coexist

## Demo Page

Access the demo at `/system/modal_tab_demo` (requires system access) to see:
- Basic tab creation
- Form handling in tabs
- Multiple tab creation
- Pin functionality demonstration
- Keyboard shortcuts

## Browser Compatibility

- Modern browsers supporting Alpine.js 3.x
- HTMX 2.0.3 compatible
- Uses standard CSS (Tailwind classes)

## Performance Notes

- Tabs store content in memory (Alpine.js reactive data)
- Large numbers of tabs may impact performance
- Consider implementing tab lazy loading for heavy content
- Pin functionality prevents automatic cleanup

## Troubleshooting

### Tabs Not Creating
1. Check `data-tab-title` attribute is present
2. Verify `hx-target="#modal_body"` is set
3. Ensure JavaScript files are loaded in correct order

### Content Not Updating
1. Check server response format
2. Verify HTMX headers are correct
3. Use browser dev tools to inspect HTMX requests

### Keyboard Shortcuts Not Working
1. Ensure modal is open and focused
2. Check for JavaScript errors in console
3. Verify modal-tab-handler.js is loaded

## Future Enhancements

Potential improvements for future versions:
- Tab drag-and-drop reordering
- Tab persistence across page reloads
- Maximum tab limits with overflow handling
- Tab groups/categories
- Custom tab icons
- Tab history/breadcrumbs
