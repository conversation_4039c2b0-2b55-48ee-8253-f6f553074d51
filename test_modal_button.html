<!-- Simple test button you can add to any page -->
<div class="p-4 bg-gray-100 border rounded">
    <h3 class="font-bold mb-2">Modal Tab Test</h3>
    <div class="space-x-2">
        <button 
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            hx-get="/baffletrain/autocadlt/autobooks/api/modal/test_tab"
            hx-target="#modal_tabs_container"
            hx-swap="innerHTML"
            onclick="document.querySelector('[x-data]').__x.$data.showModal = true; console.log('Opening modal with direct API call');">
            Direct API Test
        </button>
        
        <button 
            type="button"
            class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            hx-get="/baffletrain/autocadlt/autobooks/api/modal/open_content"
            hx-vals='{"title": "Test Tab", "content": "<div class=\"p-6\"><h2 class=\"text-xl font-bold\">Test Content</h2><p>This is a test tab created via open_content API.</p></div>"}'
            hx-target="#modal_tabs_container"
            hx-swap="innerHTML"
            onclick="document.querySelector('[x-data]').__x.$data.showModal = true; console.log('Opening modal with open_content API');">
            Open Content Test
        </button>
        
        <button 
            type="button"
            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            onclick="document.querySelector('[x-data]').__x.$data.showModal = false; console.log('Closing modal');">
            Close Modal
        </button>
    </div>
    <p class="text-xs text-gray-600 mt-2">Check browser console for debug messages</p>
</div>
