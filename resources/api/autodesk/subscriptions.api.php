<?php
namespace api\autodesk\subscriptions;

use autodesk_api\autodesk_api;
use autodesk_api\autodesk_subscriptions;
use Edge\Edge;
use data_table\data_table;
use system\data_importer;
use DateTime;
use system\users;

//print_rr($input_params);


function import_csv_into_database(){
    autodesk_api::import_csv_into_database(autodesk_subscriptions::get_subscription_column_mapping(), DIR_FS_CATALOG . DIRECTORY_SEPARATOR . "feeds/subscriptions.csv");
}

function search($p){
    return generate_subscription_table(criteria: ["search" => $p['search_terms'], "limit" => 50], just_body: false);
}


function data_table_filter($p){
    $criteria = data_table::api_process_criteria($p);
    // For pagination requests, return just the table body
    $just_body = isset($criteria['pagination_mode']) && $criteria['pagination_mode'];
    return generate_subscription_table(criteria: $criteria, just_body: $just_body);
}

function create_quote($p){
    $autodesk = new autodesk_api();
    $quote = $autodesk->quote->create_renewal($p['subs_subscriptionId'], $p['user_id']);
    print_rr($quote,'quoty1');
    $quote_assoc = json_decode($quote, true);
    print_rr($quote_assoc,'quoty2');
    return edge::render('quote-v3-ui-form', ['quote_data' => $quote_assoc]);
}

function send_quote($p){
    $autodesk = new autodesk_api();
    $quotedata = $p['quote'];
    $quote = $autodesk->quote->send_to_autodesk($quotedata);
    print_rr(i:['quote' => $quote,'p' => $p],co:false,full:true);
    return edge::render('layout-card', ['content' =>  $quote]);
}

function emails_send_reminders(){
    $autodesk = new autodesk_api();
    $subs = $autodesk->subscriptions->get_renewable();
    $file_path = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/views/subscriptions/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', autodesk_api::database_get_storage('subscription_renew_email_send_rules'));
    $settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
    $settings_time = autodesk_api::database_get_storage('subscription_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing subscription " . $sub['subscriptionReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->subscriptions->send_reminder_email($sub['id']);
                    //print_rr($autodesk->subscriptions->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}

function email_send_reminder(){
    $autodesk = new autodesk_api();
    $subs = $autodesk->subscriptions->get_renewable();
    $file_path = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/views/subscriptions/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', autodesk_api::database_get_storage('subscription_renew_email_send_rules'));
    $settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
    $settings_time = autodesk_api::database_get_storage('subscription_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing subscription " . $sub['subscriptionReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->subscriptions->send_reminder_email($sub['id']);
                    //print_rr($autodesk->subscriptions->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}

function flag_modal($p):string {
    return Edge::render('component-add-flag', $p);
}



function view($p){
    $autodesk = new autodesk_api();
    $matcher = new \subscription_matcher();
    $where = $where_hist = [];

    if (isset($p['subscription_number'])) {
        $where['subs.subscriptionReferenceNumber'] = ['=', $p['subscription_number']];
        $where_hist['hist.target_reference'] = ['=', $p['subscription_number']];
    } elseif (isset($p['subscription_id'])) {
        $where['subscriptionId'] = ['=', $p['subscription_id']];
        // For the history table, we need to get the subscription reference number first
        $sub_ref = $autodesk->subscription->get(['subscriptionReferenceNumber'], ['where' => ['subscriptionId' => ['=', $p['subscription_id']]]]);
        if (isset($sub_ref['subscriptionReferenceNumber'])) {
            $where_hist['hist.target_reference'] = ['=', $sub_ref['subscriptionReferenceNumber']];
        } else {
            // Fallback to using subscription_id directly
            $where_hist['hist.target_reference'] = ['=', $p['subscription_id']];
        }
    } else {
        print_rr("No subscription number or id provided");
        return;
    }

    // Get all subscription data columns we need for the unified display
    $subscription_columns = [
        // Subscription details
        'subs_id',
        'subs_subscriptionId',
        'subs_subscriptionReferenceNumber',
        'subs_status',
        'subs_startDate',
        'subs_endDate',
        'subs_term',
        'subs_quantity',
        'subs_offeringName',
        'subs_offeringId',
        'subs_offeringCode',
        'subs_autoRenew',
        'subs_billingBehavior',
        'subs_billingFrequency',
        'subs_intendedUsage',
        'subs_connectivity',
        'subs_servicePlan',
        'subs_opportunityNumber',
        'subs_recordType',
        'subs_enddatediff',
        'subs_created_at',
        'subs_last_modified',

        // End customer details
        'endcust_name',
        'endcust_account_csn',
        'endcust_primary_admin_first_name',
        'endcust_primary_admin_last_name',
        'endcust_primary_admin_email',
        'endcust_address1',
        'endcust_address2',
        'endcust_address3',
        'endcust_city',
        'endcust_state_province',
        'endcust_postal_code',
        'endcust_country',

        // Partner information
        'soldto_name',
        'soldto_account_csn',
        'solpro_name',
        'solpro_account_csn',
        'resell_name',
        'resell_account_csn'
    ];

    // Get subscription data
    $subscription = $autodesk->subscription->get($subscription_columns, ['where' => $where]);

    if (empty($subscription)) {
        echo '<div class="text-center py-8"><p class="text-gray-500">Subscription not found.</p></div>';
        return;
    }

    // Try to match with manual entries
    $matched_data = [];
    try {
        $matches = $matcher->match_subscription_data($subscription);
        if (!empty($matches)) {
            // Use the highest confidence match
            $matched_data = $matches[0];
            print_rr($matched_data, 'matched_data');
        }
    } catch (Exception $e) {
        print_rr($e->getMessage(), 'matching_error');
        // Continue without matching data
    }

    // Get history data
    $hist_columns = [
        'hist_id',
        'hist_target',
        'hist_target_reference',
        'hist_customer_csn',
        'hist_date',
        'hist_media',
        'hist_message',
        'hist_user_id',
        'hist_email_address',
        'hist_triggered_by',
        'hist_result',
        'users_name'
    ];

    // Use the new autodesk_history table instead of subscription_history
    // Add target filter to ensure we only get subscription-related history
    $where_hist['hist.target'] = ['=', 'subscription'];
    $history = $autodesk->get_history('subscription', [$p['subscription_number'], $p['subscription_id']], $hist_columns, ['where' => $where_hist]);

    // Render the new unified display
    $out = '<!-- Modal -->';
    print_rr($subscription, 'subscription_data');
    $out .= Edge::render('view-subscription_display', [
        'subscription' => $subscription,
        'matched_data' => $matched_data,
        'histdata' => $history
    ]);
    echo $out;
}

/**
 * Unified search function that searches both Autodesk API data and manual entries
 */
function unified_search($p) {
    $autodesk = new autodesk_api();
    $matcher = new \subscription_matcher();
    $search_terms = $p['search_terms'] ?? '';
    $results = [];

    if (empty($search_terms)) {
        echo '<div class="px-6 py-8 text-center"><p class="text-gray-500">Enter a search term to find subscriptions.</p></div>';
        return;
    }

    // Search Autodesk API data
    $autodesk_criteria = [
        "search" => $search_terms,
        "limit" => 25
    ];

    // Apply filters
    if (!empty($p['status_filter'])) {
        $autodesk_criteria['where']['subs.status'] = ['=', $p['status_filter']];
    }

    if (!empty($p['date_filter'])) {
        switch ($p['date_filter']) {
            case 'expired':
                $autodesk_criteria['where']['subs.endDate'] = ['<', date('Y-m-d')];
                break;
            case '30_days':
                $autodesk_criteria['where']['subs.endDate'] = ['BETWEEN', [date('Y-m-d'), date('Y-m-d', strtotime('+30 days'))]];
                break;
            case '90_days':
                $autodesk_criteria['where']['subs.endDate'] = ['BETWEEN', [date('Y-m-d'), date('Y-m-d', strtotime('+90 days'))]];
                break;
            case '1_year':
                $autodesk_criteria['where']['subs.endDate'] = ['BETWEEN', [date('Y-m-d'), date('Y-m-d', strtotime('+1 year'))]];
                break;
        }
    }

    if (!empty($p['product_filter'])) {
        $autodesk_criteria['where']['subs.offeringName'] = ['LIKE', '%' . $p['product_filter'] . '%'];
    }

    // Get Autodesk subscriptions if source filter allows
    if (empty($p['source_filter']) || $p['source_filter'] === 'autodesk' || $p['source_filter'] === 'matched') {
        try {
            $autodesk_results = generate_subscription_table($autodesk_criteria, true);
            if (!empty($autodesk_results)) {
                foreach ($autodesk_results as $result) {
                    $result['data_source'] = 'autodesk';
                    $result['match_info'] = null;

                    // Try to find matches for this subscription
                    if ($p['source_filter'] !== 'autodesk') {
                        try {
                            $matches = $matcher->match_subscription_data($result);
                            if (!empty($matches)) {
                                $result['match_info'] = $matches[0];
                                $result['data_source'] = 'matched';
                            }
                        } catch (Exception $e) {
                            // Continue without matching
                        }
                    }

                    $results[] = $result;
                }
            }
        } catch (Exception $e) {
            print_rr($e->getMessage(), 'autodesk_search_error');
        }
    }

    // Get manual entries if source filter allows
    if (empty($p['source_filter']) ||
        $p['source_filter'] === 'manual' ||
        $p['source_filter'] === 'matched' ||
        $p['source_filter'] === 'csv_table') {

        try {
            $manual_criteria = ['search' => $search_terms, 'limit' => 25];
            $manual_results = $matcher->get_all_manual_entries($manual_criteria);

            foreach ($manual_results as $result) {
                // Skip if filtering for specific source and this doesn't match
                if (!empty($p['source_filter'])) {
                    if ($p['source_filter'] === 'manual' && ($result['source_table'] ?? '') !== 'manual_entries') {
                        continue;
                    }
                    if ($p['source_filter'] === 'csv_table' && ($result['data_source'] ?? '') !== 'csv_table') {
                        continue;
                    }
                }

                $result['match_info'] = null;

                // Convert manual entry format to match subscription format
                if (($result['source_table'] ?? '') === 'manual_entries') {
                    $result['data_source'] = 'manual';
                    $result = normalize_manual_entry($result);
                } else {
                    // Already normalized by the matcher for CSV tables
                    $result['data_source'] = 'csv_table';
                }

                $results[] = $result;
            }
        } catch (Exception $e) {
            print_rr($e->getMessage(), 'manual_search_error');
        }
    }

    // Sort results by relevance and expiry date
    usort($results, function($a, $b) {
        // Prioritize matched data
        if ($a['data_source'] === 'matched' && $b['data_source'] !== 'matched') return -1;
        if ($b['data_source'] === 'matched' && $a['data_source'] !== 'matched') return 1;

        // Then by expiry date (soonest first)
        $a_date = $a['subs_endDate'] ?? $a['end_date'] ?? '9999-12-31';
        $b_date = $b['subs_endDate'] ?? $b['end_date'] ?? '9999-12-31';
        return strcmp($a_date, $b_date);
    });

    // Render results
    if (empty($results)) {
        echo '<div class="px-6 py-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No subscriptions found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search terms or filters.</p>
              </div>';
        return;
    }

    echo '<div class="divide-y divide-gray-200">';
    foreach ($results as $result) {
        echo render_subscription_search_result($result);
    }
    echo '</div>';

    // Update result count
    echo '<script>document.getElementById("result-count").textContent = "' . count($results) . '";</script>';
}

/**
 * Normalize manual entry data to match subscription format
 */
function normalize_manual_entry($entry) {
    return [
        'id' => $entry['id'],
        'subs_subscriptionReferenceNumber' => $entry['subscription_reference'] ?? $entry['reference_number'] ?? '',
        'subs_offeringName' => $entry['product_name'] ?? 'Manual Entry',
        'subs_quantity' => $entry['quantity'] ?? 1,
        'subs_startDate' => $entry['start_date'] ?? '',
        'subs_endDate' => $entry['end_date'] ?? '',
        'subs_status' => $entry['status'] ?? 'Unknown',
        'endcust_name' => $entry['company_name'] ?? '',
        'endcust_primary_admin_email' => $entry['email_address'] ?? $entry['contact_email'] ?? '',
        'contact_name' => $entry['contact_name'] ?? '',
        'notes' => $entry['notes'] ?? '',
        'data_source' => 'manual',
        'created_at' => $entry['created_at'] ?? '',
        'updated_at' => $entry['updated_at'] ?? ''
    ];
}

/**
 * Render a single subscription search result
 */
function render_subscription_search_result($result) {
    $data_source = $result['data_source'] ?? 'unknown';
    $is_matched = !empty($result['match_info']);

    // Calculate days until expiry
    $end_date = $result['subs_endDate'] ?? $result['end_date'] ?? '';
    $days_until_expiry = null;
    $expiry_class = 'text-gray-500';
    $expiry_text = 'No expiry date';

    if (!empty($end_date)) {
        $days_until_expiry = (strtotime($end_date) - time()) / (60 * 60 * 24);
        if ($days_until_expiry < 0) {
            $expiry_class = 'text-red-600';
            $expiry_text = 'Expired ' . abs(floor($days_until_expiry)) . ' days ago';
        } elseif ($days_until_expiry <= 30) {
            $expiry_class = 'text-red-600';
            $expiry_text = 'Expires in ' . floor($days_until_expiry) . ' days';
        } elseif ($days_until_expiry <= 90) {
            $expiry_class = 'text-yellow-600';
            $expiry_text = 'Expires in ' . floor($days_until_expiry) . ' days';
        } else {
            $expiry_class = 'text-green-600';
            $expiry_text = 'Expires ' . date('M j, Y', strtotime($end_date));
        }
    }

    // Data source badge
    $source_badge = '';
    switch ($data_source) {
        case 'autodesk':
            $source_badge = '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Autodesk API</span>';
            break;
        case 'manual':
            $source_badge = '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Manual Entry</span>';
            break;
        case 'csv_table':
            $source_badge = '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                CSV Table
                              </span>';
            break;
        case 'matched':
            $source_badge = '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Matched Data
                              </span>';
            break;
    }

    // Build view parameters
    $view_params = [];
    if (!empty($result['subs_subscriptionReferenceNumber'])) {
        $view_params['subscription_number'] = $result['subs_subscriptionReferenceNumber'];
    }
    if (!empty($result['subs_subscriptionId'])) {
        $view_params['subscription_id'] = $result['subs_subscriptionId'];
    }

    // Build action buttons
    $action_buttons = '';

    // View Details button (for Autodesk API data)
    if (!empty($view_params)) {
        $action_buttons .= '<button type="button"
                                class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                hx-post="' . APP_ROOT . '/api/subscriptions/view"
                                hx-swap="innerHTML"
                                hx-target="#modal_tabs_container"
                                hx-vals=\'' . json_encode($view_params) . '\'
                                @click="showModal = true">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Details
                        </button>';
    }

    // Add to Unity button (for CSV table entries)
    if (($result['source_table'] ?? '') !== 'manual_entries' &&
        ($result['data_source'] ?? '') === 'csv_table' &&
        ($result['can_add_to_unity'] ?? false)) {

        $add_to_unity_data = [
            'source_table' => $result['source_table'],
            'source_id' => $result['id'],
            'company_name' => $result['company_name'] ?? '',
            'email_address' => $result['email_address'] ?? '',
            'product_name' => $result['product_name'] ?? '',
            'subscription_reference' => $result['subscription_reference'] ?? '',
            'quantity' => $result['quantity'] ?? '',
            'start_date' => $result['start_date'] ?? '',
            'end_date' => $result['end_date'] ?? '',
            'contact_name' => $result['contact_name'] ?? ''
        ];

        $action_buttons .= '<button type="button"
                                class="ml-2 inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                hx-post="' . APP_ROOT . '/api/subscriptions/add_to_unity"
                                hx-swap="outerHTML"
                                hx-target="closest .subscription-result"
                                hx-vals=\'' . json_encode($add_to_unity_data) . '\'
                                hx-confirm="Add this entry to the unified subscription system?">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add to Unity
                        </button>';
    }

    // Add source table info for CSV entries
    $source_info = '';
    if (($result['source_table'] ?? '') !== 'manual_entries' && !empty($result['source_table'])) {
        $source_info = '<div class="mt-2 text-xs text-gray-500">
                            <span class="font-medium">Source:</span> ' . htmlspecialchars($result['source_table']) . '
                            ' . (!empty($result['table_description']) ? ' - ' . htmlspecialchars($result['table_description']) : '') . '
                        </div>';
    }

    return '<div class="subscription-result px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-3">
                            <div class="flex-1 min-w-0">
                                <h3 class="text-sm font-medium text-gray-900 truncate">
                                    ' . htmlspecialchars($result['subs_offeringName'] ?? $result['product_name'] ?? 'Unknown Product') . '
                                </h3>
                                <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                                    <span class="truncate">
                                        <span class="font-medium">Company:</span>
                                        ' . htmlspecialchars($result['endcust_name'] ?? $result['company_name'] ?? 'N/A') . '
                                    </span>
                                    ' . (!empty($result['endcust_primary_admin_email'] ?? $result['email_address']) ?
                                        '<span class="truncate">
                                            <span class="font-medium">Email:</span>
                                            ' . htmlspecialchars($result['endcust_primary_admin_email'] ?? $result['email_address']) . '
                                        </span>' : '') . '
                                    ' . (!empty($result['subs_subscriptionReferenceNumber'] ?? $result['subscription_reference']) ?
                                        '<span class="truncate">
                                            <span class="font-medium">Ref:</span>
                                            ' . htmlspecialchars($result['subs_subscriptionReferenceNumber'] ?? $result['subscription_reference']) . '
                                        </span>' : '') . '
                                </div>
                                ' . $source_info . '
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <div class="text-sm font-medium ' . $expiry_class . '">' . $expiry_text . '</div>
                            <div class="text-xs text-gray-500">
                                Qty: ' . ($result['subs_quantity'] ?? $result['quantity'] ?? 'N/A') . '
                            </div>
                        </div>
                        <div class="flex flex-col items-end space-y-2">
                            ' . $source_badge . '
                            <div class="flex items-center space-x-2">
                                ' . $action_buttons . '
                            </div>
                        </div>
                    </div>
                </div>
                ' . ($is_matched ?
                    '<div class="mt-3 p-3 bg-blue-50 rounded-md">
                        <div class="text-xs text-blue-700">
                            <span class="font-medium">Matched with manual entry:</span>
                            Confidence: ' . round(($result['match_info']['confidence'] ?? 0) * 100) . '%
                            (' . ucfirst($result['match_info']['match_type'] ?? 'unknown') . ' match)
                        </div>
                    </div>' : '') . '
            </div>';
}

/**
 * Get dashboard statistics
 */
function dashboard_stats($p) {
    $autodesk = new autodesk_api();
    $matcher = new \subscription_matcher();

    try {
        // Get total subscriptions from Autodesk
        $total_autodesk = $autodesk->subscriptions->get_all(
            ['subs_id'],
            ['limit' => 1, 'count_only' => true]
        );

        // Get expiring subscriptions (next 90 days)
        $expiring = $autodesk->subscriptions->get_all(
            ['subs_id'],
            [
                'where' => ['subs.endDate' => ['BETWEEN', [date('Y-m-d'), date('Y-m-d', strtotime('+90 days'))]]],
                'limit' => 1,
                'count_only' => true
            ]
        );

        // Get manual entries count
        $manual_entries = $matcher->get_all_manual_entries(['limit' => 1]);
        $manual_count = count($manual_entries);

        // For now, set matched count to 0 (would need to implement matching table)
        $matched_count = 0;

        $total_count = (is_array($total_autodesk) ? count($total_autodesk) : $total_autodesk) + $manual_count;
        $expiring_count = is_array($expiring) ? count($expiring) : $expiring;

        // Return the dashboard stats component
        return Edge::render('dashboard-stats', [
            'total_count' => $total_count,
            'expiring_count' => $expiring_count,
            'matched_count' => $matched_count,
            'manual_count' => $manual_count,
            'show_loading' => false
        ]);

    } catch (Exception $e) {
        print_rr($e->getMessage(), 'dashboard_stats_error');

        // Return error state component
        return Edge::render('dashboard-stats', [
            'total_count' => 'Error',
            'expiring_count' => 'Error',
            'matched_count' => 'Error',
            'manual_count' => 'Error',
            'show_loading' => false
        ]);
    }
}

/**
 * Sync Autodesk data
 */
function sync_autodesk_data($p) {
    try {
        $autodesk = new autodesk_api();
        $result = $autodesk->subscriptions->get_from_api();

        if ($result['status'] === 'success') {
            echo '<div class="rounded-md bg-green-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">
                                Data sync completed successfully!
                            </p>
                        </div>
                    </div>
                  </div>';
        } else {
            echo '<div class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">
                                Data sync failed. Please try again.
                            </p>
                        </div>
                    </div>
                  </div>';
        }
    } catch (Exception $e) {
        echo '<div class="rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">
                            Error: ' . htmlspecialchars($e->getMessage()) . '
                        </p>
                    </div>
                </div>
              </div>';
    }
}

/**
 * Add a CSV table entry to the unified subscription system
 */
function add_to_unity($p) {
    try {
        // Validate required data
        if (empty($p['source_table']) || empty($p['source_id'])) {
            echo '<div class="text-red-600 text-sm">Error: Missing source information</div>';
            return;
        }

        // Prepare data for insertion into manual_subscription_entries
        $data = [
            'company_name' => $p['company_name'] ?? '',
            'email_address' => $p['email_address'] ?? '',
            'contact_name' => $p['contact_name'] ?? '',
            'subscription_reference' => $p['subscription_reference'] ?? '',
            'product_name' => $p['product_name'] ?? '',
            'quantity' => !empty($p['quantity']) ? intval($p['quantity']) : null,
            'start_date' => !empty($p['start_date']) ? $p['start_date'] : null,
            'end_date' => !empty($p['end_date']) ? $p['end_date'] : null,
            'status' => 'Active', // Default status
            'notes' => 'Imported from CSV table: ' . $p['source_table'],
            'created_by' => $_SESSION['user_id'] ?? null,
            'updated_by' => $_SESSION['user_id'] ?? null
        ];

        // Remove empty values
        $data = array_filter($data, function($value) {
            return $value !== '' && $value !== null;
        });

        // Check if entry already exists
        $existing_check = "SELECT id FROM manual_subscription_entries WHERE ";
        $check_conditions = [];
        $check_params = [];

        if (!empty($data['subscription_reference'])) {
            $check_conditions[] = "subscription_reference = ?";
            $check_params[] = $data['subscription_reference'];
        } elseif (!empty($data['company_name']) && !empty($data['email_address'])) {
            $check_conditions[] = "company_name = ? AND email_address = ?";
            $check_params[] = $data['company_name'];
            $check_params[] = $data['email_address'];
        } else {
            $check_conditions[] = "company_name = ?";
            $check_params[] = $data['company_name'];
        }

        $existing_check .= implode(' OR ', $check_conditions);
        $existing = tcs_db_query($existing_check, $check_params);

        if (!empty($existing)) {
            echo '<div class="subscription-result px-6 py-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm text-yellow-800">Entry already exists in unified system</span>
                    </div>
                  </div>';
            return;
        }

        // Insert into manual_subscription_entries
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($data), '?');
        $values = array_values($data);

        $insert_query = "INSERT INTO manual_subscription_entries (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";

        $result = tcs_db_query($insert_query, $values);

        if ($result) {
            echo '<div class="subscription-result px-6 py-4 bg-green-50 border border-green-200 rounded-md">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <span class="text-sm font-medium text-green-800">Successfully added to unified system!</span>
                            <div class="text-xs text-green-700 mt-1">
                                ' . htmlspecialchars($data['company_name']) . ' - ' . htmlspecialchars($data['product_name'] ?? 'Unknown Product') . '
                            </div>
                        </div>
                    </div>
                  </div>';
        } else {
            echo '<div class="subscription-result px-6 py-4 bg-red-50 border border-red-200 rounded-md">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm text-red-800">Failed to add entry to unified system</span>
                    </div>
                  </div>';
        }

    } catch (Exception $e) {
        echo '<div class="subscription-result px-6 py-4 bg-red-50 border border-red-200 rounded-md">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</span>
                </div>
              </div>';
    }
}

// Keep the old parsing functions for backward compatibility with other parts of the system

function parse_tables($schema, $fields = []) {
    foreach ($schema as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $fields = parse_tables($value['content'], $fields);
            } elseif (isset($value['value'])) {
                $fields[$key] = $key;
            }
        }
    }
    return $fields;
}

function parse_layout($layout, $data) {
    foreach ($layout as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $layout[$key]['content'] = parse_layout($value['content'], $data);
            } elseif (isset($value['value'])) {
                $layout[$key]['value'] = $data[$key] ?? '';
            }
        }
    }
    return $layout;
}

function add_replacement_row($p) {
    return Edge::render('subscription-table-config-replacement-row', [
        'search' => '',
        'replace' => '',
        'is_new' => true
    ]);
}

function add_column_row($p) {
    print_rr($p, 'add_column_row');
    return Edge::render('subscription-table-config-column', [
        'index' => $p['index'],
        'column' => [
            'label' => '',
            'field' => '',
            'auto_filter' => false,
            'filters' => []
        ],
        'db_fields' => get_db_fields(),
        'is_new' => true
    ]);
}


function add_filter_row($p) {
    return Edge::render('subscription-table-config-filter-row', [
        'columnIndex' => $p['columnIndex'],
        'filterKey' => '',
        'filterValue' => '',
        'is_new' => true
    ]);
}

function save_subscription_config($p) {
    $config = [
        'table_id' => 'subscriptions',
        'db_table' => 'subs',
        'columns' => []
    ];
    print_rr($p['columns'], 'save_subscription_config');
    // Process columns
    foreach ($p['columns'] as $key => $column) {
        $columnConfig = [];
        $columnConfig['label'] = $column['label'];
        $columnConfig['field'] = strpos($column['field']['input'],',') ? explode(',',$column['field']['input']) : $column['field']['input'];
        $columnConfig['field'] = is_array($columnConfig['field']) ? array_map('trim', $columnConfig['field']) : trim($columnConfig['field']);

        // Add auto_filter if specified
        if (isset($column['auto_filter'])) {
            $columnConfig['auto_filter'] = true;
            $columnConfig['filter_limit'] = 100;
        }

        // Process filters if provided
        if (isset($column['filter_keys']) && isset($column['filter_values']) &&
            is_array($column['filter_keys']) && is_array($column['filter_values'])) {
            $filters = [];
            foreach ($column['filter_keys'] as $index => $key) {
                if (!empty($key) && isset($column['filter_values'][$index])) {
                    $filters[$key] = $column['filter_values'][$index];
                }
            }
            if (!empty($filters)) $columnConfig['filters'] = $filters;
        }

        $config['columns'][$column['field_name']] = $columnConfig;
    }

    // Process replacements
    $replacements = [];
    if (isset($p['replacement_search']) && isset($p['replacement_replace'])) {
        foreach ($p['replacement_search'] as $key => $search) {
            if (!empty($search) && isset($p['replacement_replace'][$key])) {
                $replacements[$search] = $p['replacement_replace'][$key];
            }
        }
    }

    // Save configurations
    autodesk_api::database_set_storage('subscription_table_config', json_encode($config));
    autodesk_api::database_set_storage('subscription_replacements', json_encode($replacements));

    return Edge::render('alert', [
        'type' => 'success',
        'message' => 'Configuration saved successfully'
    ]);
}