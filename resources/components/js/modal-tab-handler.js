// Minimal Modal Tab Handler for HTMX Integration
document.addEventListener('DOMContentLoaded', function () {
    console.log('Modal tab handler loaded');

    // Handle modal content requests - redirect to tab API
    document.body.addEventListener('htmx:beforeRequest', function (evt) {
        const element = evt.detail.elt;
        const tabTitle = element.getAttribute('data-tab-title');
        const target = element.getAttribute('hx-target');

        console.log('HTMX request intercepted:', {
            target: target,
            tabTitle: tabTitle,
            url: evt.detail.requestConfig.path
        });

        // If this is a modal tabs container request and has a tab title, redirect to tab API
        if (target === '#modal_tabs_container' && tabTitle) {

            // Get the original endpoint
            const originalUrl = evt.detail.requestConfig.path;
            console.log('Redirecting to tab API, original URL:', originalUrl);

            // Only redirect if this isn't already the modal API
            if (!originalUrl.includes('/api/modal/')) {
                // Create new parameters object
                const newParams = new URLSearchParams();
                newParams.append('original_endpoint', originalUrl);
                newParams.append('title', tabTitle);

                // Add any existing parameters
                if (evt.detail.requestConfig.parameters) {
                    if (evt.detail.requestConfig.parameters instanceof FormData) {
                        for (let [key, value] of evt.detail.requestConfig.parameters.entries()) {
                            newParams.append(key, value);
                        }
                    } else if (typeof evt.detail.requestConfig.parameters === 'object') {
                        for (let [key, value] of Object.entries(evt.detail.requestConfig.parameters)) {
                            newParams.append(key, value);
                        }
                    }
                }

                // Redirect to tab API
                const appRoot = window.APP_ROOT || '';
                evt.detail.requestConfig.path = appRoot + '/api/modal/open_content';
                evt.detail.requestConfig.parameters = newParams;

                console.log('Request redirected to:', evt.detail.requestConfig.path);
            }
        }
    });

    // Handle close modal trigger
    document.body.addEventListener('closeModal', function (evt) {
        console.log('Close modal triggered');
        const modalElement = document.querySelector('[x-data]');
        if (modalElement && modalElement.__x) {
            modalElement.__x.$data.showModal = false;
        }
    });

    // Simple keyboard shortcuts
    document.addEventListener('keydown', function (evt) {
        // Only handle when modal is open
        const modalElement = document.querySelector('[x-data]');
        if (!modalElement || !modalElement.__x || !modalElement.__x.$data.showModal) {
            return;
        }

        // Esc to close modal
        if (evt.key === 'Escape') {
            evt.preventDefault();
            console.log('Closing modal via Escape key');
            const appRoot = window.APP_ROOT || '';
            htmx.ajax('GET', appRoot + '/api/modal/close', {
                target: '#modal_container',
                swap: 'outerHTML'
            });
        }
    });
});

// Keyboard shortcuts for tab management
document.addEventListener('keydown', function (evt) {
    // Only handle shortcuts when modal is open
    const modalElement = document.querySelector('[x-data]');
    if (!modalElement || !modalElement.__x || !modalElement.__x.$data.showModal) {
        return;
    }

    const modalData = modalElement.__x.$data;

    // Ctrl+W or Cmd+W to close current tab
    if ((evt.ctrlKey || evt.metaKey) && evt.key === 'w') {
        evt.preventDefault();
        if (modalData.currentTab) {
            modalData.closeTab(modalData.currentTab);
        }
    }

    // Ctrl+T or Cmd+T to focus on tabs (could be extended)
    if ((evt.ctrlKey || evt.metaKey) && evt.key === 't') {
        evt.preventDefault();
        // Focus on first tab button if exists
        const firstTabButton = document.querySelector('[x-data] button[x-text="tab.title"]');
        if (firstTabButton) {
            firstTabButton.focus();
        }
    }

    // Ctrl+1-9 to switch to specific tab
    if ((evt.ctrlKey || evt.metaKey) && evt.key >= '1' && evt.key <= '9') {
        evt.preventDefault();
        const tabIndex = parseInt(evt.key) - 1;
        if (modalData.tabs[tabIndex]) {
            modalData.switchTab(modalData.tabs[tabIndex].id);
        }
    }
});

// Handle tab navigation with arrow keys
document.addEventListener('keydown', function (evt) {
    const activeElement = document.activeElement;

    // Check if we're focused on a tab button
    if (activeElement && activeElement.matches('[x-text="tab.title"]')) {
        const modalElement = document.querySelector('[x-data]');
        if (!modalElement || !modalElement.__x) return;

        const modalData = modalElement.__x.$data;
        const currentTabIndex = modalData.tabs.findIndex(tab => tab.id === modalData.currentTab);

        if (evt.key === 'ArrowLeft' && currentTabIndex > 0) {
            evt.preventDefault();
            modalData.switchTab(modalData.tabs[currentTabIndex - 1].id);
            // Focus on the new tab button
            setTimeout(() => {
                const newTabButton = document.querySelectorAll('[x-text="tab.title"]')[currentTabIndex - 1];
                if (newTabButton) newTabButton.focus();
            }, 10);
        } else if (evt.key === 'ArrowRight' && currentTabIndex < modalData.tabs.length - 1) {
            evt.preventDefault();
            modalData.switchTab(modalData.tabs[currentTabIndex + 1].id);
            // Focus on the new tab button
            setTimeout(() => {
                const newTabButton = document.querySelectorAll('[x-text="tab.title"]')[currentTabIndex + 1];
                if (newTabButton) newTabButton.focus();
            }, 10);
        }
    }
});

// Debug helper - log tab state changes
if (window.location.search.includes('debug=tabs')) {
    document.addEventListener('modal-add-tab', function (evt) {
        console.log('Tab added:', evt.detail);
    });

    document.addEventListener('modal-update-tab', function (evt) {
        console.log('Tab updated:', evt.detail);
    });
}

