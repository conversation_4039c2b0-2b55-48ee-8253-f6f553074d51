// Minimal Modal Tab Handler - Server-side processing with HTMX
document.addEventListener('DOMContentLoaded', function() {

    // Handle HTMX requests that should create tabs
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        const element = evt.detail.elt;
        const tabTitle = element.getAttribute('data-tab-title');

        // If element has data-tab-title, redirect to tab creation endpoint
        if (tabTitle && element.getAttribute('hx-target') === '#modal_body') {
            // Cancel the original request
            evt.preventDefault();

            // Make request to tab creation endpoint instead
            const originalEndpoint = element.getAttribute('hx-get') || element.getAttribute('hx-post');
            if (originalEndpoint) {
                htmx.ajax('POST', APP_ROOT + '/api/modal_tabs/load_content', {
                    values: {
                        endpoint: originalEndpoint,
                        title: tabTitle
                    },
                    target: '#modal_container',
                    swap: 'outerHTML'
                });
            }
        }
    });

    // Handle legacy modal content - convert to tabs automatically
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        // If target is modal_body and no tab title, create a tab via server
        if (evt.detail.target.id === 'modal_body' &&
            !evt.detail.elt.getAttribute('data-tab-title') &&
            evt.detail.xhr.status === 200) {

            // Cancel the swap
            evt.preventDefault();

            // Send content to server to create tab
            const content = evt.detail.xhr.responseText;
            htmx.ajax('POST', APP_ROOT + '/api/modal_tabs/add_tab', {
                values: {
                    content: content,
                    url: evt.detail.xhr.responseURL || ''
                },
                target: '#modal_container',
                swap: 'outerHTML'
            });
        }
    });

    // Handle HTMX trigger events for modal control
    document.body.addEventListener('htmx:trigger', function(evt) {
        if (evt.detail.closeModal) {
            // Dispatch Alpine.js event to close modal
            window.dispatchEvent(new CustomEvent('closeModal'));
        }

        if (evt.detail.openModal) {
            // Dispatch Alpine.js event to open modal
            window.dispatchEvent(new CustomEvent('openModal'));
        }
    });

    // Minimal keyboard shortcuts (only essential ones)
    document.addEventListener('keydown', function(evt) {
        // ESC to close modal
        if (evt.key === 'Escape') {
            const modalElement = document.querySelector('[x-data]');
            if (modalElement && modalElement.__x && modalElement.__x.$data.showModal) {
                htmx.ajax('POST', APP_ROOT + '/api/modal_tabs/close_modal');
            }
        }
    });
});
