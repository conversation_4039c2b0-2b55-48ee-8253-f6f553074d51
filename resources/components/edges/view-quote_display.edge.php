@props([
    'title' => 'Quote Details',
    'description' => '',
    'quote' => [],
    'line_items' => [],
    'histdata' => [],
    'class' => ''
])

@php
    $headerMetadata = [
        [
            'label' => 'Quote Number',
            'value' => $quote['quotes_quote_number'] ?? 'N/A'
        ],
        [
            'label' => 'Status',
            'value' => $quote['quotes_quote_status'] ?? 'Unknown',
            'class' => 'px-2 py-1 text-xs font-medium rounded-full ' .
                      (($quote['quotes_quote_status'] ?? '') === 'ACTIVE' ? 'bg-green-100 text-green-800' : 
                       (($quote['quotes_quote_status'] ?? '') === 'EXPIRED' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'))
        ]
    ];

    if (!empty($quote['quotes_quote_opportunity_number'])) {
        $headerMetadata[] = [
            'label' => 'Opportunity',
            'value' => $quote['quotes_quote_opportunity_number']
        ];
    }

    // Customer contact information
    $contactItems = [
        [
            'label' => 'Customer Name',
            'value' => $quote['endcust_name'] ?? 'N/A'
        ],
        [
            'label' => 'Customer Email',
            'value' => $quote['endcust_primary_admin_email'] ?? 'N/A',
            'link' => !empty($quote['endcust_primary_admin_email']) ? 'mailto:' . $quote['endcust_primary_admin_email'] : null
        ],
        [
            'label' => 'Quote Contact',
            'value' => $quote['quotecontact_name'] ?? 'N/A'
        ],
        [
            'label' => 'Contact Email',
            'value' => $quote['quotecontact_email'] ?? 'N/A',
            'link' => !empty($quote['quotecontact_email']) ? 'mailto:' . $quote['quotecontact_email'] : null
        ]
    ];

    // Quote details
    $quoteDetailsItems = [
        [
            'label' => 'Created Date',
            'value' => !empty($quote['quotes_quote_created_time']) ? date('M j, Y g:i A', strtotime($quote['quotes_quote_created_time'])) : 'N/A'
        ],
        [
            'label' => 'Quoted Date',
            'value' => !empty($quote['quotes_quoted_date']) ? date('M j, Y', strtotime($quote['quotes_quoted_date'])) : 'N/A'
        ],
        [
            'label' => 'Expiration Date',
            'value' => !empty($quote['quotes_quote_expiration_date']) ? date('M j, Y', strtotime($quote['quotes_quote_expiration_date'])) : 'N/A'
        ],
        [
            'label' => 'Language',
            'value' => $quote['quotes_quote_language'] ?? 'N/A'
        ],
        [
            'label' => 'Last Modified',
            'value' => !empty($quote['quotes_modified_at']) ? date('M j, Y g:i A', strtotime($quote['quotes_modified_at'])) : 'N/A'
        ]
    ];

    // Pricing information
    $pricingItems = [
        [
            'label' => 'Currency',
            'value' => $quote['quotes_quote_currency'] ?? 'N/A'
        ],
        [
            'label' => 'List Amount',
            'value' => !empty($quote['quotes_total_list_amount']) ? ($quote['quotes_quote_currency'] ?? '') . ' ' . number_format($quote['quotes_total_list_amount'], 2) : 'N/A'
        ],
        [
            'label' => 'Net Amount',
            'value' => !empty($quote['quotes_total_net_amount']) ? ($quote['quotes_quote_currency'] ?? '') . ' ' . number_format($quote['quotes_total_net_amount'], 2) : 'N/A'
        ],
        [
            'label' => 'Total Amount',
            'value' => !empty($quote['quotes_total_amount']) ? ($quote['quotes_quote_currency'] ?? '') . ' ' . number_format($quote['quotes_total_amount'], 2) : 'N/A'
        ],
        [
            'label' => 'Discount',
            'value' => !empty($quote['quotes_total_discount']) ? ($quote['quotes_quote_currency'] ?? '') . ' ' . number_format($quote['quotes_total_discount'], 2) : 'N/A'
        ],
        [
            'label' => 'Estimated Tax',
            'value' => !empty($quote['quotes_estimated_tax']) ? ($quote['quotes_quote_currency'] ?? '') . ' ' . number_format($quote['quotes_estimated_tax'], 2) : 'N/A'
        ],
        [
            'label' => 'Payment Terms',
            'value' => $quote['quotes_payment_terms_code'] ?? 'N/A'
        ],
        [
            'label' => 'Payment Description',
            'value' => $quote['quotes_payment_terms_description'] ?? 'N/A'
        ]
    ];
@endphp

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header Section -->
    <div class="bg-white shadow-sm rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold text-gray-900 truncate">
                        {{ $title }}
                    </h1>
                    <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                        @foreach($headerMetadata as $metadata)
                            <div class="mt-2 flex items-center text-sm text-gray-500">
                                <span class="font-medium">{{ $metadata['label'] }}:</span>
                                @if(isset($metadata['class']))
                                    <span class="ml-1 {{ $metadata['class'] }}">{{ $metadata['value'] }}</span>
                                @else
                                    <span class="ml-1">{{ $metadata['value'] }}</span>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="mt-4 sm:mt-0 sm:ml-4 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            hx-post="{{ APP_ROOT }}/api/quotes/flag_modal"
                            hx-swap="innerHTML"
                            hx-target="#modal_tabs_container"
                            hx-vals='{{ json_encode([
                                "target" => "quote",
                                "target_reference" => $quote['quotes_quote_number'] ?? '',
                                "customer_name" => $quote['endcust_name'] ?? '',
                                "email_address" => $quote['endcust_primary_admin_email'] ?? '',
                                "user_id" => $_SESSION['user_id'] ?? ''
                            ]) }}'
                            @click="showModal = true">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21a2 2 0 012 2v11a2 2 0 01-2 2H3z"></path>
                        </svg>
                        Add Note
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Primary Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Quote Details Card -->
            <x-component-card title="Quote Information">
                <x-component-data-list :items="$quoteDetailsItems" />
            </x-component-card>

            <!-- Customer Information Card -->
            <x-component-card title="Customer Information">
                <x-component-data-list :items="$contactItems" />
            </x-component-card>

            <!-- Pricing Information Card -->
            <x-component-card title="Pricing Information">
                <x-component-data-list :items="$pricingItems" />
            </x-component-card>

            <!-- Line Items Card -->
            <x-component-card title="Line Items">
                @if(!empty($line_items))
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Line #</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Extended Price</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final Price</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($line_items as $item)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ $item['line_number'] ?? '-' }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ $item['offering_name'] ?? '-' }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ $item['offering_code'] ?? '-' }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ $item['action'] ?? '-' }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ $item['quantity'] ?? '-' }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                            {{ !empty($item['start_date']) ? date('M j, Y', strtotime($item['start_date'])) : '-' }}
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                            {{ !empty($item['end_date']) ? date('M j, Y', strtotime($item['end_date'])) : '-' }}
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                            {{ !empty($item['unit_srp']) ? ($item['currency'] ?? 'GBP') . ' ' . number_format($item['unit_srp'], 2) : '-' }}
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                            {{ !empty($item['extended_srp']) ? ($item['currency'] ?? 'GBP') . ' ' . number_format($item['extended_srp'], 2) : '-' }}
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                            {{ !empty($item['end_user_price']) ? ($item['currency'] ?? 'GBP') . ' ' . number_format($item['end_user_price'], 2) : '-' }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-6">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No line items found</h3>
                        <p class="mt-1 text-sm text-gray-500">This quote doesn't have any line items.</p>
                    </div>
                @endif
            </x-component-card>
        </div>

        <!-- Right Column - Timeline/Activity -->
        <div class="space-y-6">
            <!-- Activity Timeline Card -->
            <x-component-card title="Activity Timeline">
                @if(!empty($histdata))
                    <x-component-activity-feed :activities="$histdata" />
                @else
                    <div class="text-center py-6">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No activity found</h3>
                        <p class="mt-1 text-sm text-gray-500">No activity history available for this quote.</p>
                    </div>
                @endif
            </x-component-card>

            <!-- System Information Card -->
            <div x-data="{ expanded: false }" class="bg-gray-50 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <button @click="expanded = !expanded"
                            class="flex items-center justify-between w-full text-left">
                        <h2 class="text-lg font-medium text-gray-700">System Information</h2>
                        <svg :class="{'rotate-180': expanded}"
                             class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div x-show="expanded" x-transition class="px-6 py-4">
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Quote ID</dt>
                            <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $quote['quotes_id'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created At</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ !empty($quote['quotes_created_at']) ? date('M j, Y g:i A', strtotime($quote['quotes_created_at'])) : 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Updated At</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ !empty($quote['quotes_updated_at']) ? date('M j, Y g:i A', strtotime($quote['quotes_updated_at'])) : 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
