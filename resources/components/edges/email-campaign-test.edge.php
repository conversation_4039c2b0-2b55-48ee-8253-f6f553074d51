@props([
    'campaign' => [],
    'template' => null
])

<div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
        <h2 class="text-lg font-medium text-gray-900">Send Test Email</h2>
        <p class="mt-1 text-sm text-gray-600">
            Send a test email for campaign: <strong>{{ $campaign['name'] }}</strong>
        </p>
    </div>

    <!-- Campaign Info -->
    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Campaign Details</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>Type:</strong> {{ ucfirst(str_replace('_', ' ', $campaign['type'])) }}</li>
                        <li><strong>From:</strong> {{ $campaign['from_name'] }} &lt;{{ $campaign['from_email'] }}&gt;</li>
                        <li><strong>Subject:</strong> {{ $campaign['subject_template'] ?: 'No subject template set' }}</li>
                        @if($template)
                        <li><strong>Template:</strong> {{ $template['name'] }} (v{{ $template['version'] }})</li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>

    @if(!$template)
    <!-- No Template Warning -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">No Template Found</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>This campaign doesn't have an active email template. You need to create a template before sending test emails.</p>
                </div>
                <div class="mt-4">
                    <x-forms-button
                        type="button"
                        label="Create Template"
                        variant="primary"
                        size="sm"
                        hx-get="{{ APP_ROOT }}/api/email_campaigns/template_editor"
                        hx-vals='{"campaign_id": {{ $campaign['id'] }}}'
                        hx-target="#modal_tabs_container"
                    />
                </div>
            </div>
        </div>
    </div>
    @else
    <!-- Test Email Form -->
    <form hx-post="{{ APP_ROOT }}/api/email_campaigns/send_test"
          hx-target="#test_result"
          hx-swap="innerHTML"
          class="space-y-4">
        
        <input type="hidden" name="campaign_id" value="{{ $campaign['id'] }}">
        
        <div>
            <x-forms-input
                type="email"
                name="test_email"
                label="Test Email Address"
                placeholder="Enter email address to send test to"
                required="true"
            />
            <p class="mt-1 text-sm text-gray-500">
                The test email will be sent to this address with sample data.
            </p>
        </div>

        <!-- Sample Data Preview -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Sample Data Used in Test</h4>
            <div class="text-sm text-gray-600 space-y-1">
                <div><strong>Customer Name:</strong> Test User</div>
                <div><strong>Product:</strong> Test Product</div>
                <div><strong>End Date:</strong> {{ date('Y-m-d', strtotime('+30 days')) }}</div>
                <div><strong>Reference Number:</strong> TEST-123456</div>
            </div>
        </div>

        <!-- Template Preview -->
        <div class="bg-white border border-gray-200 rounded-lg">
            <div class="px-4 py-3 border-b border-gray-200">
                <h4 class="text-sm font-medium text-gray-900">Email Preview</h4>
            </div>
            <div class="p-4">
                <div class="text-sm">
                    <div class="mb-2">
                        <strong>Subject:</strong> 
                        <span class="text-gray-600">{{ $campaign['subject_template'] ?: 'No subject template' }}</span>
                    </div>
                    <div class="border rounded p-3 bg-gray-50 max-h-64 overflow-y-auto">
                        @if($template['body_template'])
                            {!! substr(strip_tags($template['body_template']), 0, 500) !!}
                            @if(strlen(strip_tags($template['body_template'])) > 500)
                                <span class="text-gray-500">... (truncated)</span>
                            @endif
                        @else
                            <span class="text-gray-500 italic">No template content</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Send Button -->
        <div class="flex justify-between items-center">
            <div id="test_result" class="text-sm"></div>
            <div class="flex space-x-3">
                <x-forms-button
                    type="button"
                    label="Cancel"
                    variant="secondary"
                    @click="showModal = false"
                />
                
                <x-forms-button
                    type="submit"
                    label="Send Test Email"
                    variant="primary"
                    icon="envelope"
                />
            </div>
        </div>
    </form>
    @endif
</div>


