@props([
    'placeholder' => 'Search subscriptions by company, email, or reference number...',
    'show_filters' => true,
    'class' => ''
])

<div class="bg-white shadow-sm rounded-lg {{ $class }}">
    <!-- Search Header -->
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-lg font-medium text-gray-900">Unified Subscription Search</h2>
                <p class="mt-1 text-sm text-gray-500">Search across Autodesk API data, manual entries, and CSV uploads</p>
                <div class="mt-2 flex items-center space-x-4 text-xs text-gray-400">
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                        Autodesk API
                    </span>
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                        Manual Entries
                    </span>
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
                        Matched Data
                    </span>
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>
                        CSV Tables
                    </span>
                </div>
            </div>
            @if($show_filters)
                <div class="mt-4 sm:mt-0 sm:ml-4">
                    <button type="button" 
                            x-data="{ filtersOpen: false }"
                            @click="filtersOpen = !filtersOpen"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707v4.586a1 1 0 01-.293.707L9 19.414a1 1 0 01-.707.293H8a1 1 0 01-1-1v-6.586a1 1 0 00-.293-.707L.293 7.707A1 1 0 010 7V4z"></path>
                        </svg>
                        Filters
                    </button>
                </div>
            @endif
        </div>
    </div>

    <!-- Search Form -->
    <div class="px-6 py-4">
        <form hx-post="{{ APP_ROOT }}/api/unified/unified_search" 
              hx-target="#search-results" 
              hx-swap="innerHTML"
              hx-trigger="submit, keyup delay:500ms from:input[name='search_terms']"
              hx-indicator="#search-loading"
              class="space-y-4">
            
            <!-- Main Search Input -->
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text" 
                       name="search_terms" 
                       class="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                       placeholder="{{ $placeholder }}"
                       autocomplete="off">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <div id="search-loading" class="htmx-indicator">
                        <svg class="animate-spin h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters (Collapsible) -->
            @if($show_filters)
                <div x-data="{ filtersOpen: false }" x-show="filtersOpen" x-transition class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Status Filter -->
                        <div>
                            <label for="status_filter" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status_filter" id="status_filter" 
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">All Statuses</option>
                                <option value="ACTIVE">Active</option>
                                <option value="EXPIRED">Expired</option>
                                <option value="EXPIRING_SOON">Expiring Soon</option>
                                <option value="CANCELLED">Cancelled</option>
                            </select>
                        </div>

                        <!-- Data Source Filter -->
                        <div>
                            <label for="source_filter" class="block text-sm font-medium text-gray-700">Data Source</label>
                            <select name="source_filter" id="source_filter"
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">All Sources</option>
                                <option value="autodesk">Autodesk API</option>
                                <option value="manual">Manual Entries</option>
                                <option value="matched">Matched Data</option>
                                <option value="csv_table">CSV Tables</option>
                            </select>
                        </div>

                        <!-- Date Range Filter -->
                        <div>
                            <label for="date_filter" class="block text-sm font-medium text-gray-700">Expiry Date</label>
                            <select name="date_filter" id="date_filter" 
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">All Dates</option>
                                <option value="expired">Already Expired</option>
                                <option value="30_days">Next 30 Days</option>
                                <option value="90_days">Next 90 Days</option>
                                <option value="1_year">Next Year</option>
                            </select>
                        </div>

                        <!-- Product Filter -->
                        <div>
                            <label for="product_filter" class="block text-sm font-medium text-gray-700">Product</label>
                            <select name="product_filter" id="product_filter" 
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">All Products</option>
                                <option value="autocad">AutoCAD</option>
                                <option value="autocad_lt">AutoCAD LT</option>
                                <option value="inventor">Inventor</option>
                                <option value="revit">Revit</option>
                                <option value="3ds_max">3ds Max</option>
                                <option value="maya">Maya</option>
                            </select>
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="mt-4 flex items-center justify-between">
                        <button type="button" 
                                onclick="this.closest('form').reset(); htmx.trigger(this.closest('form'), 'submit')"
                                class="text-sm text-gray-500 hover:text-gray-700">
                            Clear Filters
                        </button>
                        <div class="text-sm text-gray-500">
                            <span class="font-medium" id="result-count">0</span> results found
                        </div>
                    </div>
                </div>
            @endif
        </form>
    </div>

    <!-- Search Results -->
    <div id="search-results" class="border-t border-gray-200">
        <!-- Initial state -->
        <div class="px-6 py-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Search Subscriptions</h3>
            <p class="mt-1 text-sm text-gray-500">Enter a search term to find subscriptions from both Autodesk API and manual entries.</p>
        </div>
    </div>
</div>

<!-- Quick Actions Floating Button -->
<div class="fixed bottom-6 right-6 z-50">
    <div x-data="{ open: false }" class="relative">
        <!-- Main FAB -->
        <button @click="open = !open" 
                class="bg-indigo-600 hover:bg-indigo-700 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg :class="{'rotate-45': open}" class="w-6 h-6 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
        </button>

        <!-- Action Menu -->
        <div x-show="open" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             class="absolute bottom-16 right-0 w-48 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5">
            <div class="py-1">
                <button type="button" 
                        hx-post="{{ APP_ROOT }}/api/unified/add_manual_entry_form"
                        hx-target="#modal_tabs_container"
                        hx-swap="innerHTML"
                        @click="showModal = true; open = false"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Manual Entry
                </button>
                <button type="button" 
                        hx-post="{{ APP_ROOT }}/api/unified/import_csv_form"
                        hx-target="#modal_tabs_container"
                        hx-swap="innerHTML"
                        @click="showModal = true; open = false"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                    </svg>
                    Import CSV
                </button>
                <button type="button" 
                        onclick="window.location.href='{{ APP_ROOT }}/subscriptions/settings'"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Settings
                </button>
            </div>
        </div>
    </div>
</div>
