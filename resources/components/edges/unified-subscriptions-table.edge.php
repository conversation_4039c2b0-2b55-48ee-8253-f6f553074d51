@props([
    'class' => ''
])

<div class="bg-white shadow-sm rounded-lg {{ $class }}">
    <!-- Table Header -->
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-lg font-medium text-gray-900">All Subscriptions</h2>
                <p class="mt-1 text-sm text-gray-500">Unified view of subscriptions from Autodesk API, manual entries, and CSV uploads</p>
                <div class="mt-2 flex items-center space-x-4 text-xs text-gray-400">
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                        Autodesk API
                    </span>
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                        Manual Entries
                    </span>
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
                        CSV Uploads
                    </span>
                    <span class="flex items-center">
                        <span class="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>
                        Matched Data
                    </span>
                </div>
            </div>
            <div class="mt-4 flex space-x-2 sm:mt-0 sm:ml-4">
                <!-- Refresh Button -->
                <button type="button" 
                        class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        hx-get="{{ APP_ROOT }}/api/unified/unified_subscriptions_table"
                        hx-target=".search_target"
                        hx-swap="innerHTML"
                        hx-indicator="#table-loading">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                 </button>
            </div>
        </div>
    </div>

    <!-- Data Table Container -->
    <div class="relative">
        <!-- Loading Indicator -->
        <div id="table-loading" class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
            <div class="flex items-center space-x-2">
                <svg class="animate-spin h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600">Loading subscriptions...</span>
            </div>
        </div>

        <!-- Table Content -->
        <div hx-get="{{ APP_ROOT }}/api/unified/unified_subscriptions_table" 
             hx-trigger="load" 
             hx-target=".search_target"
             hx-swap="innerHTML"
             hx-indicator="#table-loading">
            <div class="search_target">
                <!-- Initial loading state -->
                <div class="px-6 py-12 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Loading Subscriptions</h3>
                    <p class="mt-1 text-sm text-gray-500">Please wait while we load subscription data from all sources...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<div class="fixed bottom-6 right-6 z-50" x-data="{ open: false }">
    <!-- Main Action Button -->
    <button type="button" 
            @click="open = !open"
            class="inline-flex items-center justify-center w-14 h-14 bg-indigo-600 hover:bg-indigo-700 text-white rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
        <svg class="w-6 h-6" :class="{ 'rotate-45': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
    </button>

    <!-- Action Menu -->
    <div x-show="open" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         class="absolute bottom-16 right-0 w-48 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5">
        <div class="py-1">
            <button type="button" 
                    hx-post="{{ APP_ROOT }}/api/unified/add_manual_entry_form"
                    hx-target="#modal_tabs_container"
                    hx-swap="innerHTML"
                    @click="showModal = true; open = false"
                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Manual Entry
            </button>
            <button type="button" 
                    hx-post="{{ APP_ROOT }}/api/unified/import_csv_form"
                    hx-target="#modal_tabs_container"
                    hx-swap="innerHTML"
                    @click="showModal = true; open = false"
                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
                Import CSV
            </button>
            <button type="button" 
                    hx-post="{{ APP_ROOT }}/api/unified/sync_autodesk_data"
                    hx-target="#sync-status"
                    hx-swap="innerHTML"
                    @click="open = false"
                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Sync Autodesk Data
            </button>
        </div>
    </div>
</div>
