@props([
    'subscriptions' => [],
    'emptyTitle' => 'No subscriptions found',
    'emptyDescription' => 'This customer doesn\'t have any active subscriptions.',
    'emptyIcon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
    'showViewDetails' => true,
    'viewDetailsEndpoint' => '/api/autodesk/subscriptions/view'
])

@if(!empty($subscriptions))
    <div class="space-y-3">
        @foreach($subscriptions as $subscription)
            @php
                $dataSource = $subscription['data_source'] ?? 'autodesk';
                $sourceTable = $subscription['source_table'] ?? '';
                $isAutodesk = $dataSource === 'autodesk';
                $isCsvTable = $dataSource === 'csv_table';

                // Determine border and background colors based on source
                $borderClass = $isAutodesk ? 'border-blue-200' : 'border-green-200';
                $hoverClass = $isAutodesk ? 'hover:bg-blue-50' : 'hover:bg-green-50';
                $sourceColor = $isAutodesk ? 'text-blue-600' : 'text-green-600';
                $sourceBg = $isAutodesk ? 'bg-blue-100' : 'bg-green-100';
            @endphp

            <div class="border {{ $borderClass }} rounded-lg p-4 {{ $hoverClass }} transition-colors duration-150">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="text-sm font-medium text-gray-900 truncate">
                                {{ $subscription['subs_offeringName'] ?? $subscription['product_name'] ?? 'Unknown Product' }}
                            </h4>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $sourceBg }} {{ $sourceColor }}">
                                @if($isAutodesk)
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    Autodesk
                                @else
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
                                    </svg>
                                    CSV Data
                                @endif
                            </span>
                        </div>
                        <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                            <span>Ref: {{ $subscription['subs_subscriptionReferenceNumber'] ?? $subscription['subscription_reference'] ?? 'N/A' }}</span>
                            <span>Qty: {{ $subscription['subs_quantity'] ?? $subscription['quantity'] ?? 'N/A' }}</span>
                            <span>Expires: {{ $subscription['subs_endDate'] ?? $subscription['end_date'] ?? 'N/A' }}</span>
                            @if($isCsvTable && !empty($sourceTable))
                                <span class="text-gray-400">• From: {{ ucwords(str_replace('_', ' ', $sourceTable)) }}</span>
                            @endif
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <x-component-badge
                            :type="($subscription['subs_status'] ?? '') === 'ACTIVE' ? 'success' : 'default'"
                        >
                            {{ $subscription['subs_status'] ?? $subscription['status'] ?? 'Unknown' }}
                        </x-component-badge>
                        @if($showViewDetails && $isAutodesk)
                            <button type="button"
                                    class="text-indigo-600 hover:text-indigo-500 text-xs font-medium border-none bg-transparent hover:bg-transparent p-0"
                                    hx-post="{{ APP_ROOT }}{{ $viewDetailsEndpoint }}"
                                    hx-swap="innerHTML"
                                    hx-target="#modal_tabs_container"
                                    data-tab-title="Subscription Details"
                                    hx-vals='{{ json_encode([
                                        "subscription_number" => $subscription["subs_subscriptionReferenceNumber"] ?? "",
                                        "subscription_id" => $subscription["subs_subscriptionId"] ?? ""
                                    ]) }}'
                                    data-tab-title="Subscription {{ $subscription["subs_subscriptionReferenceNumber"] ?? $subscription["subs_subscriptionId"] ?? ""}} Details"
                                    @click="showModal = true;"
                                    >
                                View Details
                            </button>
                        @elseif($isCsvTable && ($subscription['can_add_to_unity'] ?? false))
                            <button type="button"
                                    class="text-green-600 hover:text-green-500 text-xs font-medium border-none bg-transparent hover:bg-transparent p-0"
                                    title="Add to unified system">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@else
    <x-component-empty-state 
        :title="$emptyTitle"
        :description="$emptyDescription"
        :icon="$emptyIcon"
    />
@endif
