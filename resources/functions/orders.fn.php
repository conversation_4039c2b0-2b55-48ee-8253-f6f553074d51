
<?php

use autodesk_api\autodesk_api;
use edge\Edge;
use data_table\data_table;
//function generate_quote_table(){
//    $autodesk = new autodesk_api();
//    $quotes = $autodesk->quotes->get_current();
//
//
//
//    return generate_quote_table_html($quotes);
//}

function generate_orders_table($criteria = ['limit' =>  30],$just_body = false, $just_rows = false, $htmx_oob = false): false|string {
    print_rr($criteria,'critty');
    $table_structure = [
        'table_id' => 'quotes',
        'db_table' => 'quotes',
        'columns' => [
            ["label" => "id", "field" => 'orders_quote_id' ],
            ["label" => "Name", "field" => 'cadserv_customers_name' ],
            ["label" => "email", "field" => 'cadserv_customers_email_address' ],
            ["label" => "orders_id", "field" => 'cadserv_orders_id'],
            ["label" => "Quote Status", "field" => 'orders_quoteStatus', ],
            ["label" => "Quote Number", "field" => 'orders_quoteNumber'],
            ["label" => "Quote message", "field" => 'orders_message' ],
            ["label" => "Quote Modified", "field" => 'orders_modifiedAt' ],
            ["label" => "Actions", "content" => function ($item) {
                print_rr($item,'poop');
                $buttons_html = Edge::render('forms-button', [
                    'type' => 'button',
                    'label' => 'View ',
                    'hx-post' => APP_ROOT . '/api/view',
                    'hx-swap' => 'innerHTML',
                    'hx-target' => '#modal_tabs_container',
                    'hx-vals' => [
                        'quote_number' => $item['orders_quoteNumber'],
                        'orders_id' => $item['cadserv_orders_id'],
                        'quote_id' => $item['orders_quote_id']
                    ],
                    "@click" => "showModal = true",
                    'data-toggle' => "modal",
                    'data-target' => "#quotes_modal",
                    'data-quote-number' => $item['orders_quoteNumber'],
                    'data-transaction-id' => $item['orders_transactionId']
                ]);
                switch ($item['orders_quoteStatus']) {
                    case 'Not sent to Autodesk':
                        $buttons_html .= Edge::render('forms-button', [
                            'type' => 'button',
                            'label' => 'Send to Autodesk',
                            'hx-post' => APP_ROOT . '/api/autodesk_send_quote',
                            'hx-vals' => [
                                "orders_id" => $item['cadserv_orders_id']
                            ]
                        ]);
                        break;
                    case 'Draft':
                        $buttons_html .= Edge::render('forms-button', [
                            'type' => 'button',
                            'label' => 'Finalize',
                            'hx-post' => APP_ROOT . '/api/autodesk_finalize_quote',
                            'hx-swap' => 'none',
                            'hx-vals' => [
                                "quote_number" => $item['orders_quoteNumber']
                            ]
                        ]);
                        break;
                }
                return $buttons_html;
            }]
        ]
    ];
    $default_criteria = [];


    $criteria = array_merge($default_criteria, $criteria);
    $autodesk = new autodesk_api();
    $columns = filter_db_schema($table_structure);

    $data = $autodesk->orders->get_all($columns ,$criteria);

    $filter_criteria = ['limit' => 100];
    foreach ($table_structure['columns'] as $key => $col) {
        if(!isset($col['auto_filter'])) continue;
        $table_structure['columns'][$key]['filter_data'] = $autodesk->subscriptions->get_distinct([$col['field']], $filter_criteria);
    }

    return data_table::process_data_table(
        table_structure: $table_structure,
        data: $data,
        criteria: $criteria,
        callback: __FUNCTION__
    );
}
//$db_data = Edge::filter_db_schema($table_data);
//$default_criteria = [
//    "order_by" => "orders.quote_id DESC",
//    "limit" => 30,
//];
//$criteria = array_merge($default_criteria, $criteria);
//$autodesk = new autodesk_api();
//$quotes = $autodesk->orders->get_all($db_data ,$criteria);
//// build_column_filters
//
///*foreach ( $table_data['columns'] as $key => $col ) {
//    if ( isset( $col['auto_filter'] ) ) {
//        $filter_criteria = [
//            "order_by" => $col['field'],
//            'limit' => $col['filter_limit'] ?? 10
//        ];
//        $filter_criteria = array_merge($criteria, $filter_criteria);
//        $table_data['columns'][$key]['filter_data'] = $autodesk->quotes->get_distinct([$col['field']], $filter_criteria);
//    }
//}*/
//
//$htmx_oob_out = [];
//if ($htmx_oob) {
//    $just_rows = true;
//    $htmx_oob_out = [
//        "hx-swap-oob" => "true",
//        "hx-ext" => "class-tools",
//        "hx-classes" => "add htmx-settling, remove:htmx-settling:10s"
//    ];
//}
//
//// Define the two dates
//$reminder_date ="";
//if (isset($items['mailhist_date_sent'])) {
//    $reminder_date = "{$items['mailhist_date_sent']}<br>Rule: {$items['mailhist_date_sent']} days";
//}
//
//foreach ($cols_hidden as $col) {
//    unset($row_content[$col]);
//}
//$rows = '';
//$columns = ($just_rows || $just_body) ? Edge::build_column_filters($table_data['columns'], $criteria) : $table_data['columns'];
//$data = [
//    "title" => "Quotes",
//    "description" => "",
//    "items" => $quotes,
//    "columns" => $columns,
//    "rows" => [
//        'id_prefix' => 'order_id_',
//        'id_field'=> 'orders_quote_id',
//        'extra_parameters' => $htmx_oob_out,
//    ],
//    "just_body" => $just_body,
//    "just_rows" => $just_rows,
//
//];
?>