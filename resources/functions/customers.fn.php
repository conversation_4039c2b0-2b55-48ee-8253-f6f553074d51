<?php
use autodesk_api\autodesk_api;
use edge\Edge;
use data_table\data_table;


function generate_customer_table($criteria = [],$just_body = false,$cols_hidden = []) {

    $table_structure = [
        'table_id' => 'customers',
        'db_table' => 'autodesk_accounts',
        'columns' => [
            ['label' => 'Company', 'field' => ["endcust_name", "endcust_email"]],
            ['label' => 'Customer', 'field' => ["endcust_first_name", "endcust_last_name"]],
            ['label' => 'CSN', 'field' => "endcust_account_csn"],
            ['label' => 'Primary Admin', 'field' => ["endcust_primary_admin_first_name", "endcust_primary_admin_last_name"]],
            ['label' => 'Location', 'field' => ['endcust_city', 'endcust_postal_code']],
            ['label' => 'Actions', 'content' => function ($item) {
                return Edge::render('forms-button', [
                    'type' => 'button',
                    'label' => 'View',
                    'icon' => 'book-open',
                    'icon_position' => 'replace',
                    'variant' => 'rounded-primary',
                    'hx-post' => APP_ROOT . '/api/view',
                    'hx-swap' => 'innerHTML',
                    'hx-target' => '#modal_tabs_container',
                    'hx-vals' => json_encode([
                        "csn" => $item['endcust_account_csn']
                    ]),
                    '@click' => 'showModal = true',
                    'data-tab-title' => 'Customer ' . $item['endcust_account_csn'],
                    'data_target' => '#modal_tabs_container',
                    'data_subscription_number' => $item['subs_subscriptionReferenceNumber']
                ]);
            },
            ]
        ]
    ];

    $default_criteria = [
        "order_by" => "endcust_name",
        "limit" => 300,
        "search_columns" => [
            "endcust.account_csn",
            "endcust.name",
            "endcust.address1",
            "endcust.address2",
            "endcust.address3",
            "endcust.city",
            "endcust.state_province",
            "endcust.postal_code",
            "endcust.country",
            "endcust.primary_admin_first_name",
            "endcust.primary_admin_last_name",
            "endcust.primary_admin_email",
            "endcust.team_id",
            "endcust.team_name",
            "endcust.first_name",
            "endcust.last_name",
            "endcust.email"]

    ];
    $criteria = array_merge($default_criteria, $criteria);
    $columns = filter_db_schema($table_structure);

    $autodesk = new autodesk_api();
    $data = $autodesk->customers->get_all($columns, $criteria);

    $filter_criteria = ['limit' => 10];
    foreach ($table_structure['columns'] as $key => $col) {
        if (!isset($col['auto_filter'])) continue;
        $table_structure['columns'][$key]['filter_data'] = $autodesk->customers->get_distinct([$col['field']], $filter_criteria);
    }
    return data_table::process_data_table(
        table_structure: $table_structure,
        data: $data,
        callback: __FUNCTION__,
        criteria: $criteria,
        cols_hidden: $cols_hidden,
        just_body: $just_body
    );
}