
<!-- startup_sequence.class.php > start() 26: initializing autoloader
-->

<!-- startup_sequence.class.php > start() 29: starting
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 97
array(53) {
  ["fs_app_root"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_system"]: string(81) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system"
  ["input_params"]: array(4) {
    ["table_name"]: string(22) "autodesk_subscriptions"
    ["callback"]: string(0) ""
    ["column_id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
    ["data_source"]: string(2) "30"
  }
  ["system_views"]: array(5) {
    [0]: string(6) "system"
    [1]: string(5) "login"
    [2]: string(6) "logout"
    [3]: string(14) "reset-password"
    [4]: string(8) "settings"
  }
  ["request_uri"]: string(80) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
  ["domain"]: string(21) "www.cadservices.co.uk"
  ["script_name"]: string(42) "/baffletrain/autocadlt/autobooks/index.php"
  ["fs_doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["fs_app"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_resources"]: string(84) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources"
  ["fs_api"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api"
  ["fs_classes"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes"
  ["fs_functions"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions"
  ["fs_views"]: string(90) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views"
  ["fs_config"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/config"
  ["fs_templates"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/templates"
  ["fs_components"]: string(95) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/components"
  ["fs_logs"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/logs"
  ["fs_sys_api"]: string(85) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api"
  ["fs_sys_classes"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes"
  ["fs_sys_functions"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions"
  ["fs_sys_views"]: string(87) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views"
  ["fs_sys_config"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/config"
  ["fs_sys_templates"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates"
  ["fs_sys_components"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/components"
  ["fs_sys_logs"]: string(86) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/logs"
  ["fs_uploads"]: string(83) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/"
  ["fs_cache"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["fs_temp"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["app_root"]: string(32) "/baffletrain/autocadlt/autobooks"
  ["app_path"]: string(33) "api/data_table/column_preferences"
  ["path_parts"]: array(4) {
    [0]: string(3) "api"
    [1]: string(10) "data_table"
    [2]: string(18) "column_preferences"
    [3]: string(13) "toggle_column"
  }
  ["top_level"]: string(3) "api"
  ["current_page"]: string(13) "toggle_column"
  ["fs_app_path"]: string(124) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences"
  ["fs_full_path"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/api/data_table/column_preferences"
  ["full_path"]: string(65) "baffletrain/autocadlt/autobooks/api/data_table/column_preferences"
  ["fs_full_page"]: string(138) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/toggle_column"
  ["full_page"]: string(79) "baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
  ["set_by"]: string(19) "HTTP_HX_CURRENT_URL"
  ["source_path"]: string(41) "/baffletrain/autocadlt/autobooks/autodesk"
  ["source_page"]: string(13) "subscriptions"
  ["source_path_parts"]: array(4) {
    [0]: string(11) "baffletrain"
    [1]: string(9) "autocadlt"
    [2]: string(9) "autobooks"
    [3]: string(8) "autodesk"
  }
  ["source_app_path"]: string(8) "autodesk"
  ["hx_current_url"]: string(84) "https://www.cadservices.co.uk/baffletrain/autocadlt/autobooks/autodesk/subscriptions"
  ["hx_current_url_parts"]: array(3) {
    ["scheme"]: string(5) "https"
    ["host"]: string(21) "www.cadservices.co.uk"
    ["path"]: string(55) "/baffletrain/autocadlt/autobooks/autodesk/subscriptions"
  }
  ["source_app_path_parts"]: array(1) {
    [0]: string(8) "autodesk"
  }
  ["source_fs_path"]: string(99) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk"
  ["fs_sys_db_class"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php"
  ["route_tree"]: array(6) {
    ["dashboard"]: array(7) {
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(8) {
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
      ["sub_folder"]: array(5) {
        ["customers"]: array(7) {
          ["name"]: string(9) "Customers"
          ["icon"]: string(4) "user"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["orders"]: array(7) {
          ["name"]: string(6) "Orders"
          ["icon"]: string(13) "shopping-cart"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["quotes"]: array(7) {
          ["name"]: string(6) "Quotes"
          ["icon"]: string(13) "speech_bubble"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["products"]: array(7) {
          ["name"]: string(8) "Products"
          ["icon"]: string(16) "computer_desktop"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["subscriptions"]: array(7) {
          ["name"]: string(13) "Subscriptions"
          ["icon"]: string(6) "ticket"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
      }
    }
    ["email_campaigns"]: array(7) {
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["system"]: array(8) {
      ["sub_folder"]: array(2) {
        ["logs"]: array(7) {
          ["name"]: string(4) "logs"
          ["icon"]: string(4) "code"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["data_sources"]: array(7) {
          ["name"]: string(12) "Data Sources"
          ["icon"]: string(12) "circle-stack"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(12) "data_sources"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
      }
      ["name"]: string(6) "System"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(1) {
        [0]: string(3) "dev"
      }
      ["show_navbar"]: bool(false)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["test"]: array(7) {
      ["name"]: string(4) "test"
      ["icon"]: string(8) "sketchup"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["data_sources"]: array(7) {
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(8) "database"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
  }
  ["route_list"]: array(12) {
    ["dashboard"]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["customers"]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["orders"]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["quotes"]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["products"]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["subscriptions"]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["email_campaigns"]: array(10) {
      ["id"]: int(35)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["logs"]: array(10) {
      ["id"]: int(9)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["system"]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(1) {
        [0]: string(3) "dev"
      }
      ["show_navbar"]: bool(false)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["data_sources"]: array(10) {
      ["id"]: int(39)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(8) "database"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["test"]: array(10) {
      ["id"]: int(40)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(4) "test"
      ["name"]: string(4) "test"
      ["icon"]: string(8) "sketchup"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
  }
  ["routes"]: array(13) {
    [0]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [1]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [2]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [3]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [4]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [5]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [6]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [7]: array(10) {
      ["id"]: int(35)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [8]: array(10) {
      ["id"]: int(9)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [9]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(1) {
        [0]: string(3) "dev"
      }
      ["show_navbar"]: bool(false)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [10]: array(10) {
      ["id"]: int(38)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [11]: array(10) {
      ["id"]: int(40)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(4) "test"
      ["name"]: string(4) "test"
      ["icon"]: string(8) "sketchup"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [12]: array(10) {
      ["id"]: int(39)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(8) "database"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 98
array(5) {
  ["auth_token"]: string(64) "856616a6c517de78558c5cb97ca278a2e049a0c6728611506790960c7902b3af"
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["debug_mode"]: bool(true)
  ["id_count"]: int(242)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!-- startup_sequence.class.php > start() 102: am I in debug mode?
-->

<!--
********************************************************************************************************************************************************
$row: startup_sequence.class.php > start() 106
array(1) {
  ["preferences"]: string(19) "{"debug_mode":true}"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!-- preferences: startup_sequence.class.php > start() 107: {"debug_mode":true}
-->

<!-- debug mode is: : startup_sequence.class.php > start() 108: on
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 116
array(5) {
  ["auth_token"]: string(64) "856616a6c517de78558c5cb97ca278a2e049a0c6728611506790960c7902b3af"
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["debug_mode"]: bool(true)
  ["id_count"]: int(242)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!-- index.php > global() 29: starting route
-->

<!-- router.class.php > route() 18: starting route for api/data_table/column_preferences/toggle_column
-->

<!-- router.class.php > get_api() 432: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api/data_table/column_preferences.api.php
-->

<!-- router.class.php > get_api() 432: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!-- router.class.php > get_api() 434: API file found: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!--
********************************************************************************************************************************************************
endpointy: router.class.php > route() 54
array(5) {
  ["parts"]: array(3) {
    [0]: string(3) "api"
    [1]: string(10) "data_table"
    [2]: string(18) "column_preferences"
  }
  ["endpoint"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  ["function_call"]: string(13) "toggle_column"
  ["api_result"]: array(2) {
    ["status"]: string(7) "success"
    ["path"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  }
  ["api_result_status"]: string(7) "success"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- endpoint: router.class.php > route() 150: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!-- launching layout-api with: router.class.php > route() 194: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->
<!-- launching layout-api with /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php-->
<!-- router.class.php > route() 209: regular view found
-->




<!--
********************************************************************************************************************************************************
calling: layout-api.edge.php > include() 44
array(5) {
  ["namespace"]: string(34) "api\data_table\column_preferences\"
  ["view"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  ["view_exists"]: string(4) "true"
  ["function_call"]: string(13) "toggle_column"
  ["path_parts"]: array(2) {
    [0]: string(10) "data_table"
    [1]: string(18) "column_preferences"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 131
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211
         <strong>Arguments:</strong>
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...

----------------------------------------------------------------------------
-->

<!-- calling: layout-api.edge.php > include() 52: api\data_table\column_preferences\toggle_column
-->

<!--
********************************************************************************************************************************************************
$configurationsavvy: data_table_storage.class.php > save_configuration() 56
array(5) {
  ["structure"]: array(15) {
    [0]: &array(6) {
      ["id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
      ["label"]: string(2) "Id"
      ["field"]: string(7) "subs_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(7) "subs_id"
      }
      ["visible"]: bool(false)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
      ["label"]: string(27) "SubscriptionReferenceNumber"
      ["field"]: string(32) "subs_subscriptionReferenceNumber"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(32) "subs_subscriptionReferenceNumber"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"
      ["label"]: string(14) "SubscriptionId"
      ["field"]: string(19) "subs_subscriptionId"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(19) "subs_subscriptionId"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_694e8d1f2ee056f98ee488bdc4982d73"
      ["label"]: string(8) "Quantity"
      ["field"]: string(13) "subs_quantity"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(13) "subs_quantity"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ec53a8c4f07baed5d8825072c89799be"
      ["label"]: string(6) "Status"
      ["field"]: string(11) "subs_status"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(11) "subs_status"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_4b613a54fb8fdaba94406d746a16cf09"
      ["label"]: string(9) "StartDate"
      ["field"]: string(14) "subs_startDate"
      ["filter"]: bool(true)
      ["fields"]: array(2) {
        [0]: string(14) "subs_startDate"
        [1]: string(12) "subs_endDate"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_a7f44c44b37e090a40ad2d76aa86d08d"
      ["label"]: string(7) "EndDate"
      ["field"]: string(12) "subs_endDate"
      ["filter"]: bool(true)
      ["fields"]: array(0) {
      }
      ["visible"]: bool(true)
    }
    [7]: array(6) {
      ["id"]: string(38) "col_7_490aa6e856ccf208a054389e47ce0d06"
      ["label"]: string(2) "Id"
      ["field"]: string(10) "endcust_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(10) "endcust_id"
      }
      ["visible"]: bool(true)
    }
    [8]: array(6) {
      ["id"]: string(38) "col_8_a2edb050ddd149d2e0bd932557f38be3"
      ["label"]: string(11) "Account Csn"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [9]: array(6) {
      ["id"]: string(38) "col_9_49ee3087348e8d44e1feda1917443987"
      ["label"]: string(4) "Name"
      ["field"]: string(12) "endcust_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(12) "endcust_name"
      }
      ["visible"]: bool(true)
    }
    [10]: array(6) {
      ["id"]: string(39) "col_10_bc910f8bdf70f29374f496f05be0330c"
      ["label"]: string(10) "First Name"
      ["field"]: string(18) "endcust_first_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(18) "endcust_first_name"
      }
      ["visible"]: bool(true)
    }
    [11]: array(6) {
      ["id"]: string(39) "col_11_77587239bf4c54ea493c7033e1dbf636"
      ["label"]: string(9) "Last Name"
      ["field"]: string(17) "endcust_last_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [12]: array(6) {
      ["id"]: string(39) "col_12_8184e89412e9d55fc20f501c898b327d"
      ["label"]: string(8) "Quote Id"
      ["field"]: string(18) "lastquote_quote_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(18) "lastquote_quote_id"
      }
      ["visible"]: bool(true)
    }
    [13]: array(6) {
      ["id"]: string(39) "col_13_a1a3d4145963c3c20655c504af782d58"
      ["label"]: string(12) "Quote Number"
      ["field"]: string(22) "lastquote_quote_number"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(22) "lastquote_quote_number"
      }
      ["visible"]: bool(true)
    }
    [14]: array(6) {
      ["id"]: string(39) "col_14_4edb4bac36117d2361f81056bf00423a"
      ["label"]: string(11) "Quoted Date"
      ["field"]: string(21) "lastquote_quoted_date"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(21) "lastquote_quoted_date"
      }
      ["visible"]: bool(true)
    }
  }
  ["updated_at"]: string(19) "2025-08-03 21:42:38"
  ["data_source_type"]: string(11) "data_source"
  ["data_source_id"]: int(30)
  ["hidden"]: array(1) {
    [0]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> save_configuration, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 27
         <strong>Arguments:</strong>
         0: "autodesk_subscriptions"
         1: {"structure":[{"id":"col_0_490aa6e856ccf208a054389e47ce0d06","label":"Id","field":"subs_id","filter"...
         2: 2
         3: 30
      <strong>Function:</strong> api\data_table\column_preferences\save_column_preferences, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 165
         <strong>Arguments:</strong>
         0: "autodesk_subscriptions"
         1: {"structure":[{"id":"col_0_490aa6e856ccf208a054389e47ce0d06","label":"Id","field":"subs_id","filter"...
         2: "30"
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
api_process_criteria: data_table.class.php > api_process_criteria() 299
array(3) {
  ["table_name"]: string(22) "autodesk_subscriptions"
  ["callback"]: string(0) ""
  ["data_source"]: string(2) "30"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api_process_criteria, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 65
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","data_source":"30"}
      <strong>Function:</strong> api\data_table\column_preferences\regenerate_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 167
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","data_source":"30"}
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
regenerate_debug: column_preferences.api.php > api\data_table\column_preferences\regenerate_table() 74
array(1) {
  ["regenerate_debug"]: array(3) {
    ["has_config"]: bool(true)
    ["data_source_type"]: string(11) "data_source"
    ["is_data_source"]: bool(true)
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\regenerate_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 167
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","data_source":"30"}
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
rendering_data_source_table: column_preferences.api.php > api\data_table\column_preferences\regenerate_table() 86
array(1) {
  ["rendering_data_source_table"]: bool(true)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\regenerate_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 167
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","data_source":"30"}
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"

----------------------------------------------------------------------------
-->


<!-- colsy: data-table.edge.php > include() 42: << empty string>>
-->

<!-- colsy: data-table.edge.php > include() 48: << empty string>>
-->


<input type="hidden" class="data_table_filter" name="callback" value="">
<input type="hidden" class="data_table_filter" name="table_name" value="autodesk_subscriptions">
<div class="relative">
    <div class="absolute top-0 right-0 z-10 p-2">

        <div x-data='{
    open: false,
    newColumnName: "",
    showAddColumn: false,


    initSortable() {
        this.$nextTick(() => {
            const container = this.$refs.columnList;
            if (container && window.Sortable) {
                // Main column sorting - server-side save on drag end
                new Sortable(container, {
                    animation: 150,
                    handle: ".column-drag-handle",
                    filter: ".field-item", // Don"t drag field items when dragging columns
                    onEnd: (evt) => {
                        // Send column reorder to server via HTMX
                        const columnIds = Array.from(container.querySelectorAll("[data-column-id]"))
                            .map(item => item.dataset.columnId);

                        htmx.ajax("POST", "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns", {
                            values: {
                                table_name: "autodesk_subscriptions",
                                callback: "",
                                data_source: "30",
                                column_order: JSON.stringify(columnIds)
                            },
                            target: ".data_table",
                            swap: "outerHTML"
                        });
                    }
                });

                // Field sorting within columns - server-side save on drag end
                container.querySelectorAll(".field-container").forEach(fieldContainer => {
                    new Sortable(fieldContainer, {
                        group: "fields", // Allow dragging between columns
                        animation: 150,
                        handle: ".field-drag-handle",
                        onEnd: (evt) => {
                            const fieldName = evt.item.dataset.fieldName;
                            const targetColumnId = evt.to.closest("[data-column-id]").dataset.columnId;
                            const sourceColumnId = evt.from.closest("[data-column-id]").dataset.columnId;

                            if (targetColumnId !== sourceColumnId) {
                                // Send field move to server via HTMX
                                htmx.ajax("POST", "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field", {
                                    values: {
                                        table_name: "autodesk_subscriptions",
                                        callback: "",
                                        data_source: "30",
                                        field_name: fieldName,
                                        source_column_id: sourceColumnId,
                                        target_column_id: targetColumnId
                                    },
                                    target: ".data_table",
                                    swap: "outerHTML"
                                });
                            }
                        }
                    });
                });
            }
        });
    }
}'
             x-init="$nextTick(() => initSortable())"
             class="relative inline-block text-left"
             @click.away="open = false">


            <button type="button"
                    @click="open = !open; if(open) $nextTick(() => initSortable())"
                    class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                    aria-expanded="false"
                    aria-haspopup="true">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v13.5c0 .621.504 1.125 1.125 1.125z" />
                </svg>
                Columns
                <svg class="h-4 w-4" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                </svg>
            </button>


            <div x-show="open"
                 x-transition:enter="transition ease-out duration-100"
                 x-transition:enter-start="transform opacity-0 scale-95"
                 x-transition:enter-end="transform opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-75"
                 x-transition:leave-start="transform opacity-100 scale-100"
                 x-transition:leave-end="transform opacity-0 scale-95"
                 class="absolute right-0 z-20 mt-2 w-96 max-h-screen origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden flex flex-col"
                 style="height: 80vh;"
                 role="menu"
                 aria-orientation="vertical"
                 tabindex="-1">


                <div class="p-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
                        <div class="flex gap-2">

                            <button type="button"
                                    @click="showAddColumn = !showAddColumn"
                                    class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                + Column
                            </button>
                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_all_columns"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                Show All
                            </button>
                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/hide_all_columns"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                                Hide All
                            </button>
                        </div>
                    </div>


                    <div class="flex items-center space-x-3 mb-3">
                        <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
                        <select id="data-source-select"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/update_data_source_and_columns"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-trigger="change"
                                name="data_source_selection"
                                class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="hardcoded" >
                                Default (Hardcoded Data)
                            </option>
                            <optgroup label="Other">
                                <option value="1"
                                >
                                    Autodesk_autorenew                                                                            </option>
                                <option value="2"
                                >
                                    test_send                                                                            </option>
                            </optgroup>
                            <optgroup label="Autodesk Integration">
                                <option value="30"
                                        selected>
                                    Autodesk Subscriptions                                                                                    - Complete subscription data with customer relationships                                                                            </option>
                                <option value="31"
                                >
                                    Autodesk Accounts                                                                                    - Customer account information                                                                            </option>
                                <option value="32"
                                >
                                    Expiring Subscriptions                                                                                    - Active subscriptions expiring within 90 days                                                                            </option>
                                <option value="33"
                                >
                                    Autodesk Email History                                                                                    - Email communication history                                                                            </option>
                                <option value="34"
                                >
                                    Copy of Autodesk Subscriptions                                                                                    - Complete subscription data with customer relationships                                                                            </option>
                                <option value="35"
                                >
                                    Full Autodesk Subscriptions                                                                                    - Complete subscription data with customer relationships                                                                            </option>
                            </optgroup>
                        </select>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Data Source
                    </span>
                    </div>


                    <div x-show="showAddColumn" x-transition class="mb-3 p-2 bg-blue-50 rounded border">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_column"
                              hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                              hx-target=".data_table"
                              hx-swap="outerHTML"
                              @htmx:after-request="showAddColumn = false; newColumnName = ''"
                              class="flex gap-2 items-center">
                            <input type="text"
                                   name="column_name"
                                   x-model="newColumnName"
                                   placeholder="Column name (e.g., Contact Info)"
                                   class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                                   required>
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Add
                            </button>
                            <button type="button"
                                    @click="showAddColumn = false"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>

                    <div class="text-xs text-gray-500">
                        Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
                    </div>
                </div>


                <div class="flex-1 overflow-y-auto p-4">
                    <div x-ref="columnList" class="space-y-3">
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_0_490aa6e856ccf208a054389e47ce0d06">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_0_490aa6e856ccf208a054389e47ce0d06", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Id</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_0_490aa6e856ccf208a054389e47ce0d06"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_0_490aa6e856ccf208a054389e47ce0d06"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="subs_id">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">subs_id</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_0_490aa6e856ccf208a054389e47ce0d06", "field_name": "subs_id"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_1_15cc3fba1d13d18d588ca3693361fa38">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_15cc3fba1d13d18d588ca3693361fa38", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">SubscriptionReferenceNumber</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_15cc3fba1d13d18d588ca3693361fa38"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_15cc3fba1d13d18d588ca3693361fa38"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="subs_subscriptionReferenceNumber">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">subs_subscriptionReferenceNumber</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_15cc3fba1d13d18d588ca3693361fa38", "field_name": "subs_subscriptionReferenceNumber"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_2_51980cd41cd1fc05c08fa2bc5e8a4e30">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">SubscriptionId</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="subs_subscriptionId">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">subs_subscriptionId</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30", "field_name": "subs_subscriptionId"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_3_694e8d1f2ee056f98ee488bdc4982d73">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_694e8d1f2ee056f98ee488bdc4982d73", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Quantity</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_694e8d1f2ee056f98ee488bdc4982d73"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_694e8d1f2ee056f98ee488bdc4982d73"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="subs_quantity">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">subs_quantity</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_694e8d1f2ee056f98ee488bdc4982d73", "field_name": "subs_quantity"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_4_ec53a8c4f07baed5d8825072c89799be">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_ec53a8c4f07baed5d8825072c89799be", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Status</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_ec53a8c4f07baed5d8825072c89799be"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_ec53a8c4f07baed5d8825072c89799be"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="subs_status">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">subs_status</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_ec53a8c4f07baed5d8825072c89799be", "field_name": "subs_status"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_5_4b613a54fb8fdaba94406d746a16cf09">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_4b613a54fb8fdaba94406d746a16cf09", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">StartDate</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    2 fields                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_4b613a54fb8fdaba94406d746a16cf09"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_4b613a54fb8fdaba94406d746a16cf09"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="subs_startDate">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">subs_startDate</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_4b613a54fb8fdaba94406d746a16cf09", "field_name": "subs_startDate"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="subs_endDate">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">subs_endDate</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_4b613a54fb8fdaba94406d746a16cf09", "field_name": "subs_endDate"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_6_a7f44c44b37e090a40ad2d76aa86d08d">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_a7f44c44b37e090a40ad2d76aa86d08d", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">EndDate</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    0 fields                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_a7f44c44b37e090a40ad2d76aa86d08d"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_a7f44c44b37e090a40ad2d76aa86d08d"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>




                                <div class="text-xs text-gray-400 text-center py-2">
                                    Drop fields here or use dropdown above
                                </div>
                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_7_490aa6e856ccf208a054389e47ce0d06">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_490aa6e856ccf208a054389e47ce0d06", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Id</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_490aa6e856ccf208a054389e47ce0d06"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_490aa6e856ccf208a054389e47ce0d06"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_id">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_id</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_490aa6e856ccf208a054389e47ce0d06", "field_name": "endcust_id"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_8_a2edb050ddd149d2e0bd932557f38be3">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_a2edb050ddd149d2e0bd932557f38be3", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Account Csn</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_a2edb050ddd149d2e0bd932557f38be3"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_a2edb050ddd149d2e0bd932557f38be3"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_account_csn">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_account_csn</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_a2edb050ddd149d2e0bd932557f38be3", "field_name": "endcust_account_csn"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_9_49ee3087348e8d44e1feda1917443987">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_49ee3087348e8d44e1feda1917443987", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Name</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_49ee3087348e8d44e1feda1917443987"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_49ee3087348e8d44e1feda1917443987"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_49ee3087348e8d44e1feda1917443987", "field_name": "endcust_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_10_bc910f8bdf70f29374f496f05be0330c">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_bc910f8bdf70f29374f496f05be0330c", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">First Name</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_bc910f8bdf70f29374f496f05be0330c"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_bc910f8bdf70f29374f496f05be0330c"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_first_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_first_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_bc910f8bdf70f29374f496f05be0330c", "field_name": "endcust_first_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_11_77587239bf4c54ea493c7033e1dbf636">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_77587239bf4c54ea493c7033e1dbf636", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Last Name</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_77587239bf4c54ea493c7033e1dbf636"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_77587239bf4c54ea493c7033e1dbf636"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_last_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_last_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_77587239bf4c54ea493c7033e1dbf636", "field_name": "endcust_last_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_12_8184e89412e9d55fc20f501c898b327d">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_8184e89412e9d55fc20f501c898b327d", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Quote Id</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_8184e89412e9d55fc20f501c898b327d"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_8184e89412e9d55fc20f501c898b327d"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="lastquote_quote_id">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">lastquote_quote_id</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_8184e89412e9d55fc20f501c898b327d", "field_name": "lastquote_quote_id"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_13_a1a3d4145963c3c20655c504af782d58">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_a1a3d4145963c3c20655c504af782d58", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Quote Number</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_a1a3d4145963c3c20655c504af782d58"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_a1a3d4145963c3c20655c504af782d58"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="lastquote_quote_number">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">lastquote_quote_number</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_a1a3d4145963c3c20655c504af782d58", "field_name": "lastquote_quote_number"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white"
                             data-column-id="col_14_4edb4bac36117d2361f81056bf00423a">


                            <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                                       hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_4edb4bac36117d2361f81056bf00423a", "data_source": "30"}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm font-medium text-gray-900">Quoted Date</span>
                                </label>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Filterable                                </span>
                                </div>


                                <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    1 field                                </span>
                                </div>


                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_4edb4bac36117d2361f81056bf00423a"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>


                            <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">

                                <div class="mb-2">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_4edb4bac36117d2361f81056bf00423a"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                        <option value="">+ Add field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="lastquote_quoted_date">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">lastquote_quoted_date</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_4edb4bac36117d2361f81056bf00423a", "field_name": "lastquote_quoted_date"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>


                            </div>
                        </div>
                    </div>
                </div>


                <div class="p-4 border-t border-gray-200 bg-gray-50">
                    <div class="flex justify-between items-center">
                        <div class="flex gap-2 items-center">
                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/hide_all_columns"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                                Hide All
                            </button>
                            <span class="text-xs text-gray-500">15 columns, 15 fields</span>
                        </div>
                        <button type="button"
                                @click="open = false"
                                class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                            Done
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <table class="min-w-full border-collapse search_target data_table " >
        <thead>
        <tr>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="subs_subscriptionId">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Subs SubscriptionId</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="subs_subscriptionId_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"subs_subscriptionId","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="subs_subscriptionReferenceNumber">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Subs SubscriptionReferenceNumber</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="subs_subscriptionReferenceNumber_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"subs_subscriptionReferenceNumber","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="subs_quantity">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Subs Quantity</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="subs_quantity_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"subs_quantity","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="subs_status">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Subs Status</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="subs_status_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"subs_status","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="subs_startDate">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Subs StartDate</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="subs_startDate_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"subs_startDate","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="subs_endDate">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Subs EndDate</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="subs_endDate_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"subs_endDate","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="endcust_id">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Endcust Id</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="endcust_id_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"endcust_id","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="endcust_account_csn">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Endcust Account Csn</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="endcust_account_csn_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"endcust_account_csn","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="endcust_name">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Endcust Name</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="endcust_name_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"endcust_name","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="endcust_first_name">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Endcust First Name</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="endcust_first_name_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"endcust_first_name","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="endcust_last_name">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Endcust Last Name</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="endcust_last_name_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"endcust_last_name","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="lastquote_quote_id">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Lastquote Quote Id</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="lastquote_quote_id_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"lastquote_quote_id","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="lastquote_quote_number">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Lastquote Quote Number</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="lastquote_quote_number_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"lastquote_quote_number","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="lastquote_quoted_date">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Lastquote Quoted Date</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="lastquote_quoted_date_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"lastquote_quoted_date","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
            <th scope="col"
                class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
                style="isolation: isolate;"
                data-column-field="subs_enddatediff">

                <div class="relative"
                     x-data="{
            showControls_243: false,
            open_243: false
     }"
                     @keydown.escape="onEscape"
                     @close-popover-group.window="onClosePopoverGroup">

                    <div class="group relative inline-flex items-center">
                        <button type="button"
                                class=""
                                @click="open_243 = !open_243">
                            <span>Subs Enddatediff</span>
                        </button>
                        <div class="flex items-center ml-1 whitespace-nowrap z-30">
                            <button id="subs_enddatediff_sort"
                                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-include=".data_table_filter"
                                    hx-vals='{"order_by":"subs_enddatediff","order_direction":"DESC","callback":null}'
                            >

                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-up
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                                <!-- icoonier: icons.php > icons\icon() 6: mini-chevron-down
-->
                                <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                        </div>
                    </div>

                    <div x-show="open_243"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 translate-y-1"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 translate-y-1"
                         class="fixed z-10"
                         style="display: none"
                         x-ref="panel"
                         @click.away="open_243 = false">

                        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                            </div>
                            <div class="bg-gray-50 px-8 py-6 flex justify-between">

                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0'>
        <span>
                    Clear            </span>
                                </button>
                                <!-- tag_content_start: forms-button.edge.php > include() 12: << empty integer>>
-->


                                <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0'>
        <span>
                    Apply            </span>
                                </button>            </div>
                        </div>
                    </div>
                </div>                            </th>
        </tr>
        </thead>

        <tbody class="bg-white data_table_body">


        <!--
********************************************************************************************************************************************************
itamage: data-table.edge.php > include() 175
array(16) {
  ["subs_id"]: int(263)
  ["subs_subscriptionId"]: string(14) "**************"
  ["subs_subscriptionReferenceNumber"]: string(12) "574-********"
  ["subs_quantity"]: int(1)
  ["subs_status"]: string(6) "Active"
  ["subs_startDate"]: string(10) "2024-07-05"
  ["subs_endDate"]: string(10) "2025-07-04"
  ["endcust_id"]: int(927)
  ["endcust_account_csn"]: int(**********)
  ["endcust_name"]: string(21) "Blue Aardvark Joinery"
  ["endcust_first_name"]: string(7) "Michael"
  ["endcust_last_name"]: string(6) "Smiddy"
  ["lastquote_quote_id"]: NULL
  ["lastquote_quote_number"]: NULL
  ["lastquote_quoted_date"]: NULL
  ["subs_enddatediff"]: int(-30)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 131
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","criteria":[],"column_preferences":{"structure":[{"id":"col_0...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 88
         <strong>Arguments:</strong>
         0: "data-table"
         1: {"table_name":"autodesk_subscriptions","criteria":[],"column_preferences":{"structure":[{"id":"col_0...

----------------------------------------------------------------------------
-->
        <tr class="border-t border-gray-300 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                **************                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-05                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-04                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                927                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Blue Aardvark Joinery                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Michael                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Smiddy                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -30                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                562-96821892                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2019-09-14                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-04                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                1133                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Welland Design and Build                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Mark                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Collins                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -30                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                59402775909072                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                569-57473650                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2020-07-06                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-05                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                3335                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Surrey Tech Services Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Dan                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Parsons                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -29                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                65710518586028                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                573-17118867                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2022-07-06                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-05                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                13880                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                KERRY JANE INTERIORS                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Ella                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Clarkson                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -29                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72043461460795                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-93530292                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-08                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-07                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                844                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                HGCE Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Andrew                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Chandler                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -27                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72044011235970                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-93544051                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-08                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-07                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                8156                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Beardmax Limitet                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                barrie                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Randall                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -27                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72043551659715                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-93532668                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-08                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-07                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                844                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                HGCE Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Andrew                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Chandler                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -27                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                59420216122899                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                569-59178474                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2020-07-08                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-07                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                35                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                360 Prism Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Wojciech                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Ziarek                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -27                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                62573700009321                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                572-19399186                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2021-07-08                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-07                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                5358                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Roger Betts                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Roger                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Betts                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -27                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                566-80439472                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2019-11-06                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-08                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                881                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                STORTFORD HOLDINGS Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Alexander                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Kannemeyer                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -26                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72061198481012                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-94018361                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-10                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-09                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                4757                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                SP Joinery Design solutions                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Shaun                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Pilcher                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -25                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72068960955842                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-94152181                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-11                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-10                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                5655                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Artium Construction Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Dean                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Welsh                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -24                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72069101199696                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-94161683                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-11                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-10                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                769                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                ADR Consulting                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Alexis                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Rouzee                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -24                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                68908367409003                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-11224705                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2023-07-11                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-10                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                8837                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                MELIUS HOMES Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Paul                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Clark                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -24                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72069101189195                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-94161782                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-11                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-10                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                769                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                ADR Consulting                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Alexis                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Rouzee                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -24                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                63725243529653                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                572-62544390                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2021-11-18                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-11                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                881                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                STORTFORD HOLDINGS Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Alexander                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Kannemeyer                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -23                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                561-63146984                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2019-06-15                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-12                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                3148                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Graham Heath Construction Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Graham                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Hanson                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -22                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                65788578572999                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                573-19330566                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2022-07-15                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-14                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                52                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                EDM London                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Dominik                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Koziel                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -20                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                68958994651888                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-12406719                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2023-07-17                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-16                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                2302                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Project Marble Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Brendan                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                McNulty                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -18                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                75014667058610                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Suspended                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2019-09-25                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-16                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                4223                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                JONATHAN LEES ARCHITECTS LLP                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Rebecca                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Blackwell                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -18                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                65816088254577                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                573-19751230                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2022-07-18                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-17                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                1245                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                DLG Architects Leeds                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Allen                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Norris                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -17                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                56346356602218                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                566-87318554                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2019-07-18                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-17                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                5134                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                JAMESDODDRELL:ARCHITECT Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                James                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Doddrell                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -17                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                68967440531689                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-12577755                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                3                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2023-07-18                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-17                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                140                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                ATELIERS DE FRANCE                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                ADF                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                london                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -17                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72139763248455                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-95439412                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                4                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-19                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-18                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                2973                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                ADF Paris                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Dominik                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Koziel                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -16                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                62679454918073                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                572-31811921                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2021-07-20                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-19                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                633                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                CAMB MACHINE KNIVES                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Neil                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Chapman                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -15                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                59534173428567                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                569-67099416                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2020-07-21                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-20                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                1026                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                COVENTRY CONSTRUCTION Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Roman                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Pundyk                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -14                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72120676149648                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-94938079                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-22                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-21                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                6483                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Wiveliscombe Joinery Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Phil                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Burchett                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -13                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72163602001312                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-95603322                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-22                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-21                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                6483                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Wiveliscombe Joinery Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Phil                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Burchett                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -13                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72182154545092                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-96089708                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                2                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-24                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-23                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                4808                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                V4 ARCHITECTS Ltd                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                simon                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                harmsworth                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -11                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72182061735999                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-96087431                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-24                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-23                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                144                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Civils & Construction Solutions                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Liam                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Jackson                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -11                                                                        </td>
        </tr>
        <tr class="border-t border-gray-200 " id="row_" >
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionId">
                72191338436576                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_subscriptionReferenceNumber">
                574-96315974                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_quantity">
                1                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_status">
                Active                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_startDate">
                2024-07-25                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_endDate">
                2025-07-24                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_id">
                6998                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_account_csn">
                **********                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_name">
                Stair Formwork                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_first_name">
                Natalia                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="endcust_last_name">
                Bagniewska                                                                        </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_id">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quote_number">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="lastquote_quoted_date">
            </td>
            <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell"
                data-column-field="subs_enddatediff">
                -10                                                                        </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
            <td colspan="16">


                <div class="flex bottom-0 items-center justify-between border-t border-gray-200 bg-white px-4 py-2 sm:px-6">
                    <div class="flex flex-1 justify-between sm:hidden">
                        <a href="#"
                           hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                           hx-target=".search_target"
                           hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                           hx-vals='{"page": 1}'
                           class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
                        <a href="#"
                           hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                           hx-target=".search_target"
                           hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                           hx-vals='{"page": 2}'
                           class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
                    </div>
                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing
                                <span class="font-medium">1</span>
                                to
                                <span class="font-medium">30</span>
                                of
                                <span class="font-medium">3748 </span>
                                results
                            </p>
                        </div>
                        <div>
                            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                <a href="#"
                                   hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                                   hx-target=".search_target"
                                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                                   hx-vals='{"page": 1}'
                                   class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    <span class="sr-only">Previous</span>
                                    <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                        <path fill-rule="evenodd"
                                              d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                </a>

                                <a href="#"
                                   hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                                   hx-target=".search_target"
                                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                                   hx-vals='{"page": 1}'
                                   aria-current="page" class="relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                    1                        </a>
                                <a href="#"
                                   hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                                   hx-target=".search_target"
                                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                                   hx-vals='{"page": 2}'
                                   class="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex">
                                    2                        </a>
                                <a href="#"
                                   hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                                   hx-target=".search_target"
                                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                                   hx-vals='{"page": 3}'
                                   class="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex">
                                    3                        </a>
                                <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">...</span>
                                <a href="#"
                                   hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                                   hx-target=".search_target"
                                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                                   hx-vals='{"page": 125}'
                                   class="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex">
                                    125                        </a>


                                <a href="#"
                                   hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                                   hx-target=".search_target"
                                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                                   hx-vals='{"page": 2}'
                                   class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    <span class="sr-only">Next</span>
                                    <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                        <path fill-rule="evenodd"
                                              d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>                </td>
        </tr>
        </tfoot>
    </table>
</div>

