<div class="border-b border-gray-200 mb-4"><nav class="-mb-px flex space-x-8" aria-label="Tabs"><div class="flex items-center group"><button role="tab" aria-controls="tab-contents" aria-selected="false" hx-get="/baffletrain/autocadlt/autobooks/api/modal/switch_tab" hx-vals='{"tab_id": "tab_68912dba160db"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">Test Tab 1</button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/toggle_pin" hx-vals='{"tab_id": "tab_68912dba160db"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded hover:bg-gray-100 text-gray-400 hover:text-gray-600" title="Pin tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg></button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/close_tab" hx-vals='{"tab_id": "tab_68912dba160db"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100" title="Close tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg></button></div><div class="flex items-center group"><button role="tab" aria-controls="tab-contents" aria-selected="false" hx-get="/baffletrain/autocadlt/autobooks/api/modal/switch_tab" hx-vals='{"tab_id": "tab_68912dba16100"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">Test Tab 2</button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/toggle_pin" hx-vals='{"tab_id": "tab_68912dba16100"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded hover:bg-gray-100 text-gray-400 hover:text-gray-600" title="Pin tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg></button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/close_tab" hx-vals='{"tab_id": "tab_68912dba16100"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100" title="Close tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg></button></div><div class="flex items-center group"><button role="tab" aria-controls="tab-contents" aria-selected="false" hx-get="/baffletrain/autocadlt/autobooks/api/modal/switch_tab" hx-vals='{"tab_id": "tab_68912e3213783"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">Test Tab 1</button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/toggle_pin" hx-vals='{"tab_id": "tab_68912e3213783"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded hover:bg-gray-100 text-gray-400 hover:text-gray-600" title="Pin tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg></button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/close_tab" hx-vals='{"tab_id": "tab_68912e3213783"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100" title="Close tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg></button></div><div class="flex items-center group"><button role="tab" aria-controls="tab-contents" aria-selected="true" hx-get="/baffletrain/autocadlt/autobooks/api/modal/switch_tab" hx-vals='{"tab_id": "tab_68912e321379a"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-indigo-500 text-indigo-600">Test Tab 2</button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/toggle_pin" hx-vals='{"tab_id": "tab_68912e321379a"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded hover:bg-gray-100 text-gray-400 hover:text-gray-600" title="Pin tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg></button><button hx-get="/baffletrain/autocadlt/autobooks/api/modal/close_tab" hx-vals='{"tab_id": "tab_68912e321379a"}' hx-target="#modal_tabs_container" hx-swap="innerHTML" class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100" title="Close tab"><svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg></button></div></nav></div><div id="tab-contents" role="tabpanel"><div class="p-4">Test content 2</div></div>