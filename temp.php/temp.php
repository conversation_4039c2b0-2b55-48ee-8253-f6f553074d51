<div x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
     class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full sm:p-6">


    <div class="absolute top-0 right-0 pt-4 pr-4">
        <button type="button" hx-get="/baffletrain/autocadlt/autobooks/api/modal/close" hx-target="#modal_container"
                hx-swap="outerHTML"
                class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <span class="sr-only">Close</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>


    <div id="modal_tabs_container" hx-target="#tab-contents" role="tablist" hx-on:htmx:after-on-load="
                     let currentTab = document.querySelector('[aria-selected=true]');
                     if (currentTab) {
                         currentTab.setAttribute('aria-selected', 'false');
                         currentTab.classList.remove('border-indigo-500', 'text-indigo-600');
                         currentTab.classList.add('border-transparent', 'text-gray-500');
                     }
                     let newTab = event.target;
                     newTab.setAttribute('aria-selected', 'true');
                     newTab.classList.remove('border-transparent', 'text-gray-500');
                     newTab.classList.add('border-indigo-500', 'text-indigo-600');
                 " class="">

        <!-- Modal -->


        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="bg-white shadow-sm rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div class="flex-1 min-w-0">
                            <h1 class="text-2xl font-bold text-gray-900 truncate">
                                BSBA TEES Ltd </h1>
                            <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                                <div class="mt-2 flex items-center text-sm text-gray-500">
                                    <span class="font-medium">CSN:</span>
                                    <span class="ml-1 ">5112098456</span>
                                </div>
                                <div class="mt-2 flex items-center text-sm text-gray-500">
                                    <span class="font-medium">Type:</span>
                                    <span class="ml-1 px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">End Customer</span>
                                </div>
                                <div class="mt-2 flex items-center text-sm text-gray-500">
                                    <span class="font-medium">Team:</span>
                                    <span class="ml-1 ">David Brown - 8539</span>
                                </div>
                            </div>
                        </div>
                        <!-- rest of content snipped to save space -->
                    </div>
                </div>
            </div>
        </div>


        <style>
            @media print {
                .no-print {
                    display: none !important;
                }

                .print-break {
                    page-break-before: always;
                }

                body {
                    font-size: 12px;
                }

                .shadow-sm {
                    box-shadow: none !important;
                }

                .bg-white {
                    background: white !important;
                }

                .text-indigo-600 {
                    color: #000 !important;
                }

                .bg-indigo-600 {
                    background: #000 !important;
                }

                .border-gray-200 {
                    border-color: #ccc !important;
                }
            }
        </style>

    </div>
</div>