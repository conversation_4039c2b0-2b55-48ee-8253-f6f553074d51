<?php
// Debug script for modal tabs
session_start();

require_once 'system/classes/ModalTabManager.php';

echo "<h1>Modal Tab Debug</h1>";

// Test 1: Check session
echo "<h2>Test 1: Session Status</h2>";
echo "Session status: " . session_status() . "<br>";
echo "Session ID: " . session_id() . "<br>";

// Test 2: Check tabs in session
echo "<h2>Test 2: Current Tabs in Session</h2>";
$tabs = ModalTabManager::getTabs();
echo "<pre>" . print_r($tabs, true) . "</pre>";

// Test 3: Add a test tab
echo "<h2>Test 3: Add Test Tab</h2>";
$tabId = ModalTabManager::addTab('Test Tab', '<div class="p-4">Test content</div>', '');
echo "Added tab ID: " . $tabId . "<br>";

// Test 4: Check tabs after adding
echo "<h2>Test 4: Tabs After Adding</h2>";
$tabs = ModalTabManager::getTabs();
echo "<pre>" . print_r($tabs, true) . "</pre>";

// Test 5: Render tabs
echo "<h2>Test 5: Rendered Tabs HTML</h2>";
$html = ModalTabManager::renderTabs();
echo "<textarea rows='20' cols='100'>" . htmlspecialchars($html) . "</textarea>";

// Test 6: Display rendered tabs
echo "<h2>Test 6: Rendered Tabs Display</h2>";
echo $html;

// Test 7: Clear tabs
echo "<h2>Test 7: Clear Tabs</h2>";
ModalTabManager::clearTabs();
echo "Tabs cleared<br>";

$tabs = ModalTabManager::getTabs();
echo "Tabs after clearing: <pre>" . print_r($tabs, true) . "</pre>";
?>
