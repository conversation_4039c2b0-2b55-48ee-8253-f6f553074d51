<?php
// Debug script for modal tabs


require_once 'system/startup_sequence_minimal.php';
session_start();
require_once 'system/classes/ModalTabManager.php';

echo "<h1>Modal Tab Debug</h1>";

// Test 1: Check session
echo "<h2>Test 1: Session Status</h2>";
echo "Session status: " . session_status() . "<br>";
echo "Session ID: " . session_id() . "<br>";

// Test 2: Check tabs in session
echo "<h2>Test 2: Current Tabs in Session</h2>";
$tabs = ModalTabManager::getTabs();
echo "<pre>" . print_r($tabs, true) . "</pre>";

// Test 3: Add test tabs
echo "<h2>Test 3: Add Test Tabs</h2>";
$tabId1 = ModalTabManager::addTab('Test Tab 1', '<div class="p-4">Test content 1</div>', '');
echo "Added tab ID 1: " . $tabId1 . "<br>";

$tabId2 = ModalTabManager::addTab('Test Tab 2', '<div class="p-4">Test content 2</div>', '');
echo "Added tab ID 2: " . $tabId2 . "<br>";

// Test 4: Check tabs after adding
echo "<h2>Test 4: Tabs After Adding</h2>";
$tabs = ModalTabManager::getTabs();
echo "<pre>" . print_r($tabs, true) . "</pre>";

// Test 5: Render tabs
echo "<h2>Test 5: Rendered Tabs HTML</h2>";
$html = ModalTabManager::renderTabs();
echo "<textarea rows='20' cols='100'>" . htmlspecialchars($html) . "</textarea>";

// Test 6: Display rendered tabs
echo "<h2>Test 6: Rendered Tabs Display</h2>";
echo '<div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9;">';
echo $html;
echo '</div>';

// Test 6.5: Test openContent method
echo "<h2>Test 6.5: Test openContent Method</h2>";
$openContentResult = ModalTabManager::openContent('Open Content Test', '<div class="p-4 bg-green-100">Content from openContent method</div>', '');
echo "<textarea rows='10' cols='100'>" . htmlspecialchars($openContentResult) . "</textarea>";
echo '<div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9;">';
echo $openContentResult;
echo '</div>';

// Test 7: Clear tabs
echo "<h2>Test 7: Clear Tabs</h2>";
ModalTabManager::clearTabs();
echo "Tabs cleared<br>";

$tabs = ModalTabManager::getTabs();
echo "Tabs after clearing: <pre>" . print_r($tabs, true) . "</pre>";
?>
