@props([
    'title' => 'Email Campaign Management',
    'description' => 'Create and manage mass email campaigns'
])

@php
    use system\email_campaign;

    $campaign_manager = new email_campaign();
    $campaigns = $campaign_manager->get_campaigns();

    // Campaign statistics
    $active_campaigns = 0;
    $draft_campaigns = 0;
    foreach ($campaigns as $campaign) {
        if ($campaign['status'] === 'active') $active_campaigns++;
        if ($campaign['status'] === 'draft') $draft_campaigns++;
    }

    $stats = [
        'total_campaigns' => count($campaigns),
        'active_campaigns' => $active_campaigns,
        'draft_campaigns' => $draft_campaigns,
        'total_sent' => array_sum(array_column($campaigns, 'total_sent'))
    ];
@endphp
<div class="bg-gray-50 px-4 sm:px-6 lg:px-8 py-8 space-y-6">

    <!-- Page Header -->
    <div class="border-b border-gray-200 pb-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $title }}</h1>
                <p class="mt-1 text-sm text-gray-600">{{ $description }}</p>
            </div>
            <div class="flex space-x-3">
                <x-forms-button
                        type="button"
                        label="Create Campaign"
                        icon="plus"
                        variant="primary"
                        hx-get="{{ APP_ROOT }}/api/email_campaigns/create_modal"
                        hx-target="#modal_body"
                        data-tab-title="Create Campaign"
                        @click="if (!showModal) { showModal = true; }"
                />
            </div>
        </div>
    </div>


    <!-- Campaign Statistics -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Campaigns</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['total_campaigns'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Campaigns</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['active_campaigns'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Draft Campaigns</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['draft_campaigns'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Emails Sent</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['total_sent']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
                <div>
                    <x-forms-input
                            type="search"
                            name="campaign_search"
                            placeholder="Search campaigns..."
                            hx-get="{{ APP_ROOT }}/api/email_campaigns/search"
                            hx-trigger="input changed delay:500ms, search"
                            hx-target="#campaigns_table"
                            hx-include="[name=&quot;status_filter&quot;], [name=&quot;type_filter&quot;]"
                    />
                </div>

                <div>
                    <x-forms-select
                            name="status_filter"
                            label="Status"
                            :options="[
                            '' => 'All Statuses',
                            'draft' => 'Draft',
                            'active' => 'Active',
                            'paused' => 'Paused',
                            'completed' => 'Completed',
                            'archived' => 'Archived'
                        ]"
                            hx-get="{{ APP_ROOT }}/api/email_campaigns/search"
                            hx-trigger="change"
                            hx-target="#campaigns_table"
                            hx-include="[name=&quot;campaign_search&quot;], [name=&quot;type_filter&quot;]"
                    />
                </div>

                <div>
                    <x-forms-select
                            name="type_filter"
                            label="Type"
                            :options="[
                            '' => 'All Types',
                            'general' => 'General',
                            'promotional' => 'Promotional',
                            'notification' => 'Notification',
                            'subscription_renewal' => 'Subscription Renewal'
                        ]"
                            hx-get="{{ APP_ROOT }}/api/email_campaigns/search"
                            hx-trigger="change"
                            hx-target="#campaigns_table"
                            hx-include="[name=&quot;campaign_search&quot;], [name=&quot;status_filter&quot;]"
                    />
                </div>

                <div class="flex items-end">
                    <x-forms-button
                            type="button"
                            label="Refresh"
                            icon="refresh"
                            variant="secondary"
                            hx-get="{{ APP_ROOT }}/api/email_campaigns/get_campaigns"
                            hx-target="#campaigns_table"
                    />
                </div>
            </div>
        </div>
    </div>

    <!-- Template Management -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Email Templates</h3>
            <p class="text-sm text-gray-600 mb-4">Manage email templates used by campaigns.</p>

            <x-email-template-selector
                    name="template"
                    label="Select Template to Edit"
                    selected=""
                    :campaign_id="null"
                    :show_actions="true"
                    :show_data_source="false"
            />
        </div>
    </div>

    <!-- Campaigns Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div id="campaigns_table"
                 hx-get="{{ APP_ROOT }}/api/email_campaigns/get_campaigns"
                 hx-trigger="load, refreshCampaigns from:body"
                 hx-swap="innerHTML">
                <!-- Table will be loaded here -->
                <div class="flex justify-center items-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span class="ml-2 text-gray-600">Loading campaigns...</span>
                </div>
            </div>
        </div>
    </div>


</div>

{{--<script>--}}
{{--    // Handle notifications--}}
{{--    document.body.addEventListener('showNotification', function (event) {--}}
{{--        const notification = event.detail;--}}
{{--        // You can integrate with your existing notification system here--}}
{{--        console.log(notification.type + ': ' + notification.message);--}}

{{--        // Simple alert for now - replace with your notification system--}}
{{--        if (notification.type === 'error') {--}}
{{--            alert('Error: ' + notification.message);--}}
{{--        } else {--}}
{{--            alert(notification.message);--}}
{{--        }--}}
{{--    });--}}

{{--    // Handle modal closing--}}
{{--    document.body.addEventListener('closeModal', function (event) {--}}
{{--        // Close the campaign modal using Alpine.js--}}
{{--        const modalElement = document.querySelector('[x-data]');--}}
{{--        if (modalElement && modalElement.__x) {--}}
{{--            modalElement.__x.$data.showCampaignModal = false;--}}
{{--        }--}}
{{--    });--}}

{{--    // Handle opening new windows--}}
{{--    document.body.addEventListener('openWindow', function (event) {--}}
{{--        const url = event.detail.url;--}}
{{--        if (url) {--}}
{{--            window.open(url, '_blank');--}}
{{--        }--}}
{{--    });--}}

{{--    // Handle form submission success - close modal after successful save--}}
{{--    document.body.addEventListener('refreshCampaigns', function () {--}}
{{--        setTimeout(() => {--}}
{{--            const modalElement = document.querySelector('[x-data]');--}}
{{--            if (modalElement && modalElement.__x) {--}}
{{--                modalElement.__x.$data.showCampaignModal = false;--}}
{{--            }--}}
{{--        }, 1000);--}}
{{--    });--}}
{{--</script>--}}
