@props([
    'title' => 'Modal Tab Demo',
    'description' => 'Demonstration of the new tab functionality in the system-wide modal'
])

<div class="bg-gray-50 px-4 sm:px-6 lg:px-8 py-8 space-y-6">
    <!-- Page Header -->
    <div class="border-b border-gray-200 pb-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $title }}</h1>
                <p class="mt-1 text-sm text-gray-600">{{ $description }}</p>
            </div>
        </div>
    </div>

    <!-- Demo Section -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-semibold mb-4">Tab Functionality Demo</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <!-- Direct Test Tab -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium mb-2">Direct Test</h3>
                <p class="text-sm text-gray-600 mb-3">Direct tab creation test (no redirection).</p>
                <x-forms-button
                    type="button"
                    label="Test Tab System"
                    variant="success"
                    hx-get="{{ APP_ROOT }}/api/modal/test_tab"
                    hx-target="#modal_tabs_container"
                    hx-swap="innerHTML"
                    data-tab-title="Direct Test"
                    @click="showModal = true"
                />
            </div>

            <!-- Basic Tab Demo -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium mb-2">Basic Tab</h3>
                <p class="text-sm text-gray-600 mb-3">Opens a simple tab with demo content.</p>
                <x-forms-button
                    type="button"
                    label="Open Demo Tab"
                    variant="primary"
                    hx-get="{{ APP_ROOT }}/api/modal_tab_demo/demo_tab"
                    hx-target="#modal_tabs_container"
                    hx-swap="innerHTML"
                    data-tab-title="Demo Tab"
                    @click="showModal = true"
                />
            </div>

            <!-- Form Tab Demo -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium mb-2">Form Tab</h3>
                <p class="text-sm text-gray-600 mb-3">Opens a tab containing a form.</p>
                <x-forms-button
                    type="button"
                    label="Open Form Tab"
                    variant="secondary"
                    hx-get="{{ APP_ROOT }}/api/modal_tab_demo/form_tab"
                    hx-target="#modal_tabs_container"
                    hx-swap="innerHTML"
                    data-tab-title="Form Example"
                    @click="showModal = true"
                />
            </div>

            <!-- Multiple Tabs Demo -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium mb-2">Multiple Tabs</h3>
                <p class="text-sm text-gray-600 mb-3">Opens several tabs at once.</p>
                <x-forms-button
                    type="button"
                    label="Open Multiple"
                    variant="outline"
                    @click="
                        // Open first tab
                        $dispatch('modal-add-tab', {
                            title: 'Tab 1',
                            content: '<div class=\'p-6\'><h2 class=\'text-xl font-bold mb-4\'>First Tab</h2><p>This is the first tab content.</p></div>',
                            url: ''
                        });
                        // Open second tab
                        setTimeout(() => {
                            $dispatch('modal-add-tab', {
                                title: 'Tab 2',
                                content: '<div class=\'p-6\'><h2 class=\'text-xl font-bold mb-4\'>Second Tab</h2><p>This is the second tab content.</p></div>',
                                url: ''
                            });
                        }, 100);
                        // Open third tab
                        setTimeout(() => {
                            $dispatch('modal-add-tab', {
                                title: 'Tab 3',
                                content: '<div class=\'p-6\'><h2 class=\'text-xl font-bold mb-4\'>Third Tab</h2><p>This is the third tab content.</p></div>',
                                url: ''
                            });
                        }, 200);
                    "
                />
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-blue-800 font-semibold mb-2">How to Use Tabs</h3>
            <div class="text-blue-700 text-sm space-y-2">
                <p><strong>Tab Management Rules:</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                    <li><strong>Modal Closed:</strong> Opening content creates a single tab and displays it</li>
                    <li><strong>Modal Open:</strong> New content always creates additional tabs (never overwrites existing tabs)</li>
                    <li><strong>Pin Functionality:</strong> Click the pin icon to mark important tabs (visual indicator only)</li>
                    <li><strong>Tab Closing:</strong> Use individual X buttons to close specific tabs, or global X to close all</li>
                </ul>
                
                <p class="mt-3"><strong>Keyboard Shortcuts:</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                    <li><kbd class="px-1 py-0.5 bg-blue-100 rounded text-xs">Ctrl+W</kbd> - Close current tab</li>
                    <li><kbd class="px-1 py-0.5 bg-blue-100 rounded text-xs">Ctrl+1-9</kbd> - Switch to tab by number</li>
                    <li><kbd class="px-1 py-0.5 bg-blue-100 rounded text-xs">←/→</kbd> - Navigate tabs when tab button is focused</li>
                    <li><kbd class="px-1 py-0.5 bg-blue-100 rounded text-xs">Esc</kbd> - Close entire modal</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Technical Implementation -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-semibold mb-4">Technical Implementation</h2>
        
        <div class="prose prose-sm max-w-none">
            <p>The tab functionality is implemented using:</p>
            
            <ul>
                <li><strong>Alpine.js</strong> for reactive tab state management</li>
                <li><strong>HTMX</strong> for server-side content loading with minimal JavaScript</li>
                <li><strong>Tailwind CSS</strong> for styling</li>
                <li><strong>OOB-swap approach</strong> for updating tab bar and content</li>
            </ul>
            
            <h3>For Developers:</h3>
            <p>To make a button create tabs, add these attributes:</p>
            
            <pre class="bg-gray-100 p-3 rounded text-xs overflow-x-auto"><code>hx-get="your-endpoint"
hx-target="#modal_tabs_container"
data-tab-title="Your Tab Title"
@click="if (!showModal || !tabs.some(t => t.pinned)) { showModal = true; }"</code></pre>
            
            <p>The JavaScript handler automatically detects the <code>data-tab-title</code> attribute and creates tabs accordingly.</p>
        </div>
    </div>
</div>
