<?php
use edge\Edge;
use data_table\data_table;

function generate_user_table($criteria = [], $just_body = false) {
    $table_structure = [
        'columns' => [
            ['label' => 'Name',    'field' => "name"],
            ['label' => 'Email',       'field' => "email"],
            ['label' => 'Role',        'field' => "role", 'auto_filter' => true],
            ['label' => 'Status',      'field' => "status", 'auto_filter' => true],
            ['label' => 'Last Login',  'field' => "last_login"],
            ['label' => 'Actions',     'content' => function ($item) {
                return Edge::render('forms-button', [
                    'type' => 'button',
                    'label' => 'Edit',
                    'icon' => 'pencil',
                    'icon_position' => 'replace',
                    'variant' => 'rounded-primary',
                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/user_modal',
                    'hx-target' => '#modal_tabs_container',
                    'hx-vals' => json_encode(['user_id' => $item['id']]),
                    '@click' => 'showModal = true'
                ]) .
                Edge::render('forms-button', [
                    'type' => 'button',
                    'label' => 'Reset Password',
                    'icon' => 'key',
                    'icon_position' => 'replace',
                    'variant' => 'rounded-warning',
                    'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/reset_password',
                    'hx-confirm' => 'Are you sure you want to reset this user\'s password?',
                    'hx-vals' => json_encode(['user_id' => $item['id']]),
                    'hx-target' => '#password_reset_message'
                ]);
            }]
        ]
    ];


    $data =  tep_db_fetch_all(tep_db_query("SELECT * FROM autobooks_users"));

    $filter_criteria = ['limit' => 100];
    foreach ($table_structure['columns'] as $key => $col) {
        if(!isset($col['auto_filter'])) continue;
        $table_structure['columns'][$key]['filter_data'] = tep_db_fetch_all(tep_db_query("SELECT DISTINCT {$col['field']} FROM autobooks_users"));
    }
    echo data_table::process_data_table(
        table_structure: $table_structure,
        data: $data,
        callback: __FUNCTION__,
        criteria: $criteria,
        just_body: $just_body
    );
}