<?php
namespace api\modal_tabs;

require_once 'system/classes/ModalTabState.php';
use edge\Edge;

/**
 * Add content as a new tab
 */
function add_tab($params = []) {
    $title = $params['title'] ?? '';
    $content = $params['content'] ?? '';
    $url = $params['url'] ?? '';
    
    // If no title provided, try to extract from content
    if (!$title && $content) {
        $title = \ModalTabState::extractTitle($content);
    }
    
    $tabId = \ModalTabState::addTab($title, $content, $url);
    
    // Return the complete modal HTML with OOB swap
    echo '<div id="modal_container" hx-swap-oob="outerHTML">';
    echo \ModalTabState::renderModal();
    echo '</div>';
}

/**
 * Switch to a specific tab
 */
function switch_tab($params = []) {
    $tabId = $params['tab_id'] ?? 0;
    
    if (\ModalTabState::switchTab($tabId)) {
        // Return updated modal content
        echo '<div id="modal_body" hx-swap-oob="innerHTML">';
        echo \ModalTabState::getCurrentTabContent();
        echo '</div>';
        
        // Also update tab bar to show active state
        echo '<div id="modal_tab_bar" hx-swap-oob="outerHTML">';
        echo renderTabBar();
        echo '</div>';
    } else {
        http_response_code(400);
        echo 'Tab not found';
    }
}

/**
 * Close a specific tab
 */
function close_tab($params = []) {
    $tabId = $params['tab_id'] ?? 0;
    
    if (\ModalTabState::closeTab($tabId)) {
        $state = \ModalTabState::getState();
        
        if (empty($state['tabs'])) {
            // No tabs left, close modal
            header('HX-Trigger: closeModal');
            echo '';
        } else {
            // Return updated modal
            echo '<div id="modal_container" hx-swap-oob="outerHTML">';
            echo \ModalTabState::renderModal();
            echo '</div>';
        }
    } else {
        http_response_code(400);
        echo 'Tab not found';
    }
}

/**
 * Toggle pin status of a tab
 */
function toggle_pin($params = []) {
    $tabId = $params['tab_id'] ?? 0;
    
    $pinned = \ModalTabState::togglePin($tabId);
    
    if ($pinned !== false) {
        // Return updated tab bar
        echo '<div id="modal_tab_bar" hx-swap-oob="outerHTML">';
        echo renderTabBar();
        echo '</div>';
    } else {
        http_response_code(400);
        echo 'Tab not found';
    }
}

/**
 * Close all tabs and modal
 */
function close_modal($params = []) {
    \ModalTabState::closeModal();
    
    header('HX-Trigger: closeModal');
    echo '';
}

/**
 * Load content into a new tab (main endpoint for HTMX requests)
 */
function load_content($params = []) {
    $endpoint = $params['endpoint'] ?? '';
    $title = $params['title'] ?? '';
    
    if (!$endpoint) {
        http_response_code(400);
        echo 'No endpoint specified';
        return;
    }
    
    // Make internal request to get content
    $content = fetchEndpointContent($endpoint, $params);
    
    if (!$title) {
        $title = \ModalTabState::extractTitle($content);
    }
    
    $tabId = \ModalTabState::addTab($title, $content, $endpoint);
    
    // Return complete modal with new tab
    echo '<div id="modal_container" hx-swap-oob="outerHTML">';
    echo \ModalTabState::renderModal();
    echo '</div>';
    
    // Trigger modal open
    header('HX-Trigger: openModal');
}

/**
 * Update current tab content
 */
function update_current_tab($params = []) {
    $content = $params['content'] ?? '';
    $title = $params['title'] ?? null;
    
    if (\ModalTabState::updateCurrentTab($content, $title)) {
        // Return updated content
        echo '<div id="modal_body" hx-swap-oob="innerHTML">';
        echo $content;
        echo '</div>';
        
        // If title was updated, refresh tab bar
        if ($title) {
            echo '<div id="modal_tab_bar" hx-swap-oob="outerHTML">';
            echo renderTabBar();
            echo '</div>';
        }
    } else {
        http_response_code(400);
        echo 'No active tab to update';
    }
}

/**
 * Render the tab bar HTML
 */
function renderTabBar() {
    $state = \ModalTabState::getState();
    
    if (empty($state['tabs'])) {
        return '';
    }
    
    $html = '<div id="modal_tab_bar" class="border-b border-gray-200 mb-4">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">';
    
    foreach ($state['tabs'] as $tab) {
        $isActive = $tab['id'] == $state['currentTab'];
        $activeClass = $isActive 
            ? 'border-indigo-500 text-indigo-600' 
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300';
        
        $pinIcon = $tab['pinned'] 
            ? '<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8zM9 5a2 2 0 00-2 2v6H6a1 1 0 100 2h8a1 1 0 100-2h-1V7a2 2 0 00-2-2H9zM7 8a1 1 0 012-2h2a1 1 0 110 2H9a1 1 0 01-2 0z" clip-rule="evenodd"/></svg>'
            : '<svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>';
        
        $html .= '<div class="flex items-center group">
            <button
                hx-post="' . APP_ROOT . '/api/modal_tabs/switch_tab"
                hx-vals=\'{"tab_id": ' . $tab['id'] . '}\'
                hx-target="#modal_body"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ' . $activeClass . '">' 
                . htmlspecialchars($tab['title']) . 
            '</button>
            
            <button
                hx-post="' . APP_ROOT . '/api/modal_tabs/toggle_pin"
                hx-vals=\'{"tab_id": ' . $tab['id'] . '}\'
                class="ml-2 p-1 rounded hover:bg-gray-100 ' . ($tab['pinned'] ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-600') . '"
                title="' . ($tab['pinned'] ? 'Unpin tab' : 'Pin tab') . '">
                ' . $pinIcon . '
            </button>
            
            <button
                hx-post="' . APP_ROOT . '/api/modal_tabs/close_tab"
                hx-vals=\'{"tab_id": ' . $tab['id'] . '}\'
                class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                title="Close tab">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>';
    }
    
    $html .= '</nav></div>';
    
    return $html;
}

/**
 * Fetch content from an internal endpoint
 */
function fetchEndpointContent($endpoint, $params = []) {
    // This would make an internal request to get the content
    // For now, return a placeholder
    return '<div class="p-6"><h2>Content from ' . htmlspecialchars($endpoint) . '</h2><p>This would be the actual content from the endpoint.</p></div>';
}
