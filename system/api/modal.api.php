<?php
namespace api\modal;

require_once 'system/classes/ModalTabManager.php';

/**
 * Close the entire modal
 */
function close($params = []) {
    \ModalTabManager::clearTabs();
    
    // Return empty modal container (effectively hiding it)
    echo '<div id="modal_container" style="display: none;"></div>';
}

/**
 * Switch to a specific tab
 */
function switch_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::switchTab($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Close a specific tab
 */
function close_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::closeTab($tabId);
    }
    
    $tabs = \ModalTabManager::getTabs();
    if (empty($tabs)) {
        // No tabs left, close modal
        header('HX-Trigger: closeModal');
        echo '<div id="tab-contents" role="tabpanel"><!-- No tabs --></div>';
    } else {
        echo \ModalTabManager::renderTabs();
    }
}

/**
 * Toggle pin status of a tab
 */
function toggle_pin($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::togglePin($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Open content in a new tab
 */
function open_content($params = []) {
    // Force debug output to browser
    echo '<div class="bg-red-100 p-4 mb-4 text-sm"><strong>DEBUG:</strong> open_content called with params: <pre>' . print_r($params, true) . '</pre></div>';

    // Also log to error log
    error_log("MODAL API: open_content called with params: " . print_r($params, true));

    $title = $params['title'] ?? 'New Tab';
    $originalEndpoint = $params['original_endpoint'] ?? '';
    $url = $params['url'] ?? $originalEndpoint;

    echo '<div class="bg-blue-100 p-4 mb-4 text-sm"><strong>DEBUG:</strong> Title: ' . htmlspecialchars($title) . ', Original endpoint: ' . htmlspecialchars($originalEndpoint) . '</div>';
    error_log("MODAL API: Title: {$title}, Original endpoint: {$originalEndpoint}");

    // Fetch content from original endpoint
    $content = '';
    if ($originalEndpoint) {
        echo '<div class="bg-green-100 p-4 mb-4 text-sm"><strong>DEBUG:</strong> Fetching content from endpoint...</div>';
        // Parse the endpoint and call the appropriate function
        $content = fetchEndpointContent($originalEndpoint, $params);
        echo '<div class="bg-green-100 p-4 mb-4 text-sm"><strong>DEBUG:</strong> Content fetched, length: ' . strlen($content) . '</div>';
    } else {
        $content = $params['content'] ?? '<p>No content provided</p>';
    }

    error_log("MODAL API: Content length: " . strlen($content));

    echo '<div class="bg-purple-100 p-4 mb-4 text-sm"><strong>DEBUG:</strong> Calling ModalTabManager::openContent...</div>';
    $result = \ModalTabManager::openContent($title, $content, $url);
    echo '<div class="bg-purple-100 p-4 mb-4 text-sm"><strong>DEBUG:</strong> ModalTabManager result length: ' . strlen($result) . '</div>';

    error_log("MODAL API: ModalTabManager result length: " . strlen($result));

    echo $result;
}

/**
 * Fetch content from an endpoint
 */
function fetchEndpointContent($endpoint, $params = []) {
    // Debug output to browser
    echo '<div class="bg-yellow-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Called with endpoint: ' . htmlspecialchars($endpoint) . '</div>';

    // Debug logging
    error_log("MODAL API: fetchEndpointContent called with endpoint: " . $endpoint);
    error_log("MODAL API: fetchEndpointContent params: " . print_r($params, true));

    // Remove APP_ROOT from endpoint if present
    echo '<div class="bg-yellow-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Original endpoint: ' . htmlspecialchars($endpoint) . '</div>';
    $endpoint = str_replace(APP_ROOT, '', $endpoint);
    echo '<div class="bg-yellow-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> After APP_ROOT removal: ' . htmlspecialchars($endpoint) . '</div>';
    $endpoint = ltrim($endpoint, '/');
    echo '<div class="bg-yellow-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> After ltrim: ' . htmlspecialchars($endpoint) . '</div>';

    // Parse endpoint path
    $pathParts = explode('/', $endpoint);
    echo '<div class="bg-yellow-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Path parts: ' . print_r($pathParts, true) . '</div>';

    // Remove 'api' if it's the first part
    if ($pathParts[0] === 'api') {
        array_shift($pathParts);
        echo '<div class="bg-yellow-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Removed "api", new parts: ' . print_r($pathParts, true) . '</div>';
    }

    error_log("MODAL API: Path parts after processing: " . print_r($pathParts, true));

    if (count($pathParts) >= 2) {
        $module = $pathParts[0];
        $action = $pathParts[1];

        error_log("Looking for module: {$module}, action: {$action}");

        // Look for the API file in system/api
        $apiFile = "system/api/{$module}.api.php";
        error_log("Checking system API file: " . $apiFile);

        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            error_log("Trying function: " . $functionName);

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    $content = ob_get_clean();
                    error_log("Successfully got content, length: " . strlen($content));
                    return $content;
                } catch (Exception $e) {
                    ob_end_clean();
                    error_log("Error calling function: " . $e->getMessage());
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
                error_log("Function {$functionName} does not exist");
            }
        } else {
            error_log("API file {$apiFile} does not exist");
        }

        // Try app-level API
        $apiFile = "api/{$module}.api.php";
        error_log("Checking app API file: " . $apiFile);

        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    return ob_get_clean();
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }

        // Try direct file inclusion for non-API endpoints
        $viewFile = "system/views/{$module}/{$action}.edge.php";
        if (file_exists($viewFile)) {
            error_log("Found view file: " . $viewFile);
            ob_start();
            try {
                // Simple include - you might need to adjust this based on your view system
                include $viewFile;
                return ob_get_clean();
            } catch (Exception $e) {
                ob_end_clean();
                error_log("Error including view: " . $e->getMessage());
            }
        }
    }

    // Special handling for single-word endpoints like 'view'
    if (count($pathParts) === 1) {
        $action = $pathParts[0];

        // Try resources/api first (where view.api.php is located)
        $apiFile = "resources/api/{$action}.api.php";
        echo '<div class="bg-orange-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Checking resources API file: ' . htmlspecialchars($apiFile) . '</div>';
        error_log("MODAL API: Checking resources API file: " . $apiFile);

        if (file_exists($apiFile)) {
            echo '<div class="bg-orange-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> File exists, requiring...</div>';
            require_once $apiFile;

            // For view.api.php, the function is in the api namespace
            $functionName = "api\\{$action}";
            echo '<div class="bg-orange-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Trying function: ' . htmlspecialchars($functionName) . '</div>';
            error_log("MODAL API: Trying function: " . $functionName);

            if (function_exists($functionName)) {
                echo '<div class="bg-orange-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Function exists, calling with params: ' . print_r($params, true) . '</div>';

                // Enable error reporting to catch any issues
                $old_error_reporting = error_reporting(E_ALL);
                $old_display_errors = ini_get('display_errors');
                ini_set('display_errors', 1);

                ob_start();
                try {
                    echo '<div class="bg-blue-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> About to call function...</div>';
                    $functionName($params);
                    echo '<div class="bg-blue-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Function call completed</div>';
                    $content = ob_get_clean();
                    echo '<div class="bg-green-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Successfully got content, length: ' . strlen($content) . '</div>';
                    error_log("MODAL API: Successfully got content from resources API, length: " . strlen($content));

                    // Restore error reporting
                    error_reporting($old_error_reporting);
                    ini_set('display_errors', $old_display_errors);

                    return $content;
                } catch (Exception $e) {
                    ob_end_clean();
                    echo '<div class="bg-red-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Exception caught: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    error_log("MODAL API: Error calling resources API function: " . $e->getMessage());

                    // Restore error reporting
                    error_reporting($old_error_reporting);
                    ini_set('display_errors', $old_display_errors);

                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                } catch (Error $e) {
                    ob_end_clean();
                    echo '<div class="bg-red-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Fatal error caught: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    error_log("MODAL API: Fatal error calling resources API function: " . $e->getMessage());

                    // Restore error reporting
                    error_reporting($old_error_reporting);
                    ini_set('display_errors', $old_display_errors);

                    return '<div class="p-4 text-red-600">Fatal error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
                echo '<div class="bg-red-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> Function does not exist</div>';
                error_log("MODAL API: Function {$functionName} does not exist in resources API");
            }
        } else {
            echo '<div class="bg-red-100 p-2 mb-2 text-xs"><strong>FETCH DEBUG:</strong> File does not exist</div>';
            error_log("MODAL API: Resources API file {$apiFile} does not exist");
        }

        // Try system API
        $apiFile = "system/api/{$action}.api.php";
        if (file_exists($apiFile)) {
            require_once $apiFile;

            // Try to find a default function
            $functionName = "api\\{$action}\\index";
            if (!function_exists($functionName)) {
                $functionName = "api\\{$action}\\{$action}";
            }

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    return ob_get_clean();
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }

        // Try app-level API
        $apiFile = "api/{$action}.api.php";
        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$action}\\index";
            if (!function_exists($functionName)) {
                $functionName = "api\\{$action}\\{$action}";
            }

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    return ob_get_clean();
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }
    }

    $errorMsg = 'Error: Could not load content from ' . htmlspecialchars($endpoint) . '. Available path parts: ' . implode(', ', $pathParts);
    error_log($errorMsg);
    return '<div class="p-4 text-red-600">' . $errorMsg . '</div>';
}

/**
 * Get current tab state for debugging
 */
function debug_tabs($params = []) {
    $tabs = \ModalTabManager::getTabs();

    echo '<pre>' . print_r($tabs, true) . '</pre>';
}

/**
 * Simple test endpoint to create a tab directly
 */
function test_tab($params = []) {
    $title = $params['title'] ?? 'Test Tab';
    $content = '
    <div class="p-6">
        <h2 class="text-xl font-bold mb-4">' . htmlspecialchars($title) . '</h2>
        <p class="mb-4">This is a test tab created directly without endpoint redirection.</p>
        <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-blue-800">✓ Tab system is working!</p>
        </div>
        <button
            type="button"
            class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            hx-get="' . APP_ROOT . '/api/modal/test_tab"
            hx-vals=\'{"title": "Another Test Tab"}\'
            hx-target="#modal_tabs_container"
            hx-swap="innerHTML">
            Create Another Test Tab
        </button>
    </div>';

    echo \ModalTabManager::openContent($title, $content, '');
}
