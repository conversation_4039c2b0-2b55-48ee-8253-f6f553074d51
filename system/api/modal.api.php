<?php
namespace api\modal;

require_once 'system/classes/ModalTabManager.php';

/**
 * Close the entire modal
 */
function close($params = []) {
    \ModalTabManager::clearTabs();
    
    // Return empty modal container (effectively hiding it)
    echo '<div id="modal_container" style="display: none;"></div>';
}

/**
 * Switch to a specific tab
 */
function switch_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::switchTab($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Close a specific tab
 */
function close_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::closeTab($tabId);
    }
    
    $tabs = \ModalTabManager::getTabs();
    if (empty($tabs)) {
        // No tabs left, close modal
        header('HX-Trigger: closeModal');
        echo '<div id="tab-contents" role="tabpanel"><!-- No tabs --></div>';
    } else {
        echo \ModalTabManager::renderTabs();
    }
}

/**
 * Toggle pin status of a tab
 */
function toggle_pin($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::togglePin($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Open content in a new tab
 */
function open_content($params = []) {
    $title = $params['title'] ?? 'New Tab';
    $originalEndpoint = $params['original_endpoint'] ?? '';
    $url = $params['url'] ?? $originalEndpoint;

    // Fetch content from original endpoint
    $content = '';
    if ($originalEndpoint) {
        // Parse the endpoint and call the appropriate function
        $content = fetchEndpointContent($originalEndpoint, $params);
    } else {
        $content = $params['content'] ?? '<p>No content provided</p>';
    }

    echo \ModalTabManager::openContent($title, $content, $url);
}

/**
 * Fetch content from an endpoint
 */
function fetchEndpointContent($endpoint, $params = []) {
    // Remove APP_ROOT from endpoint if present
    $endpoint = str_replace(APP_ROOT, '', $endpoint);
    $endpoint = ltrim($endpoint, '/');

    // Parse endpoint path
    $pathParts = explode('/', $endpoint);

    // Remove 'api' if it's the first part
    if ($pathParts[0] === 'api') {
        array_shift($pathParts);
    }

    if (count($pathParts) >= 2) {
        $module = $pathParts[0];
        $action = $pathParts[1];

        // Look for the API file
        $apiFile = "system/api/{$module}.api.php";
        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            if (function_exists($functionName)) {
                ob_start();
                $functionName($params);
                return ob_get_clean();
            }
        }

        // Try app-level API
        $apiFile = "api/{$module}.api.php";
        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            if (function_exists($functionName)) {
                ob_start();
                $functionName($params);
                return ob_get_clean();
            }
        }
    }

    return '<div class="p-4 text-red-600">Error: Could not load content from ' . htmlspecialchars($endpoint) . '</div>';
}

/**
 * Get current tab state for debugging
 */
function debug_tabs($params = []) {
    $tabs = \ModalTabManager::getTabs();
    
    echo '<pre>' . print_r($tabs, true) . '</pre>';
}
