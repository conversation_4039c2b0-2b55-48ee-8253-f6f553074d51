<?php
namespace api\modal;

require_once 'system/classes/ModalTabManager.php';

/**
 * Close the entire modal
 */
function close($params = []) {
    \ModalTabManager::clearTabs();
    
    // Return empty modal container (effectively hiding it)
    echo '<div id="modal_container" style="display: none;"></div>';
}

/**
 * Switch to a specific tab
 */
function switch_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::switchTab($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Close a specific tab
 */
function close_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::closeTab($tabId);
    }
    
    $tabs = \ModalTabManager::getTabs();
    if (empty($tabs)) {
        // No tabs left, close modal
        header('HX-Trigger: closeModal');
        echo '<div id="tab-contents" role="tabpanel"><!-- No tabs --></div>';
    } else {
        echo \ModalTabManager::renderTabs();
    }
}

/**
 * Toggle pin status of a tab
 */
function toggle_pin($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::togglePin($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Open content in a new tab
 */
function open_content($params = []) {
    // Debug logging
    error_log("open_content called with params: " . print_r($params, true));

    $title = $params['title'] ?? 'New Tab';
    $originalEndpoint = $params['original_endpoint'] ?? '';
    $url = $params['url'] ?? $originalEndpoint;

    error_log("Title: {$title}, Original endpoint: {$originalEndpoint}");

    // Fetch content from original endpoint
    $content = '';
    if ($originalEndpoint) {
        // Parse the endpoint and call the appropriate function
        $content = fetchEndpointContent($originalEndpoint, $params);
    } else {
        $content = $params['content'] ?? '<p>No content provided</p>';
    }

    error_log("Content length: " . strlen($content));

    // Add debug info to content for troubleshooting
    if (isset($_GET['debug']) || isset($params['debug'])) {
        $content = '<div class="bg-yellow-50 p-2 mb-4 text-xs"><strong>Debug:</strong> Title: ' . htmlspecialchars($title) . ', Endpoint: ' . htmlspecialchars($originalEndpoint) . '</div>' . $content;
    }

    $result = \ModalTabManager::openContent($title, $content, $url);
    error_log("ModalTabManager result length: " . strlen($result));

    echo $result;
}

/**
 * Fetch content from an endpoint
 */
function fetchEndpointContent($endpoint, $params = []) {
    // Debug logging
    error_log("fetchEndpointContent called with endpoint: " . $endpoint);
    error_log("fetchEndpointContent params: " . print_r($params, true));

    // Remove APP_ROOT from endpoint if present
    $endpoint = str_replace(APP_ROOT, '', $endpoint);
    $endpoint = ltrim($endpoint, '/');

    // Parse endpoint path
    $pathParts = explode('/', $endpoint);

    // Remove 'api' if it's the first part
    if ($pathParts[0] === 'api') {
        array_shift($pathParts);
    }

    error_log("Path parts after processing: " . print_r($pathParts, true));

    if (count($pathParts) >= 2) {
        $module = $pathParts[0];
        $action = $pathParts[1];

        error_log("Looking for module: {$module}, action: {$action}");

        // Look for the API file in system/api
        $apiFile = "system/api/{$module}.api.php";
        error_log("Checking system API file: " . $apiFile);

        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            error_log("Trying function: " . $functionName);

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    $content = ob_get_clean();
                    error_log("Successfully got content, length: " . strlen($content));
                    return $content;
                } catch (Exception $e) {
                    ob_end_clean();
                    error_log("Error calling function: " . $e->getMessage());
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
                error_log("Function {$functionName} does not exist");
            }
        } else {
            error_log("API file {$apiFile} does not exist");
        }

        // Try app-level API
        $apiFile = "api/{$module}.api.php";
        error_log("Checking app API file: " . $apiFile);

        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    return ob_get_clean();
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }
    }

    $errorMsg = 'Error: Could not load content from ' . htmlspecialchars($endpoint) . '. Available path parts: ' . implode(', ', $pathParts);
    error_log($errorMsg);
    return '<div class="p-4 text-red-600">' . $errorMsg . '</div>';
}

/**
 * Get current tab state for debugging
 */
function debug_tabs($params = []) {
    $tabs = \ModalTabManager::getTabs();

    echo '<pre>' . print_r($tabs, true) . '</pre>';
}

/**
 * Simple test endpoint to create a tab directly
 */
function test_tab($params = []) {
    $title = $params['title'] ?? 'Test Tab';
    $content = '
    <div class="p-6">
        <h2 class="text-xl font-bold mb-4">' . htmlspecialchars($title) . '</h2>
        <p class="mb-4">This is a test tab created directly without endpoint redirection.</p>
        <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-blue-800">✓ Tab system is working!</p>
        </div>
        <button
            type="button"
            class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            hx-get="' . APP_ROOT . '/api/modal/test_tab"
            hx-vals=\'{"title": "Another Test Tab"}\'
            hx-target="#modal_tabs_container"
            hx-swap="innerHTML">
            Create Another Test Tab
        </button>
    </div>';

    echo \ModalTabManager::openContent($title, $content, '');
}
