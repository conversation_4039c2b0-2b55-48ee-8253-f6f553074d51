<?php
namespace api\modal_tab_demo;

require_once 'system/classes/ModalTabManager.php';
require_once 'system/classes/ModalTabState.php';

/**
 * Demo endpoint for tab functionality
 */
function demo_tab($params = []) {
    $tabNumber = $params['tab_number'] ?? rand(1, 100);
    $title = "Demo Tab #{$tabNumber}";
    
    $content = '
    <div class="p-6">
        <h2 class="text-xl font-bold mb-4">' . htmlspecialchars($title) . '</h2>
        <p class="mb-4">This is a demonstration of the tab functionality.</p>
        
        <div class="bg-blue-50 p-4 rounded-lg mb-4">
            <h3 class="font-semibold text-blue-800 mb-2">Tab Features:</h3>
            <ul class="list-disc list-inside text-blue-700 space-y-1">
                <li><strong>Default Behavior:</strong> All content opens in tabs automatically</li>
                <li><strong>Never Overwrites:</strong> New content always creates new tabs when modal is open</li>
                <li>Click the pin icon to mark important tabs (visual indicator)</li>
                <li>Use the X button to close individual tabs</li>
                <li>The global X closes all tabs and the modal</li>
            </ul>
        </div>
        
        <div class="flex space-x-3">
            <button 
                type="button"
                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                hx-get="' . APP_ROOT . '/api/modal_tab_demo/demo_tab"
                hx-vals=\'{"tab_number": "' . ($tabNumber + 1) . '"}\'
                hx-target="#modal_body"
                data-tab-title="Demo Tab #' . ($tabNumber + 1) . '">
                Open Another Tab
            </button>
            
            <button 
                type="button"
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                hx-get="' . APP_ROOT . '/api/modal_tab_demo/update_current"
                hx-vals=\'{"content": "Updated content for tab ' . $tabNumber . '"}\'
                hx-target="#modal_body">
                Update This Tab
            </button>
        </div>
        
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 class="font-semibold mb-2">Keyboard Shortcuts:</h4>
            <ul class="text-sm text-gray-600 space-y-1">
                <li><kbd class="px-2 py-1 bg-gray-200 rounded">Ctrl+W</kbd> - Close current tab</li>
                <li><kbd class="px-2 py-1 bg-gray-200 rounded">Ctrl+1-9</kbd> - Switch to tab by number</li>
                <li><kbd class="px-2 py-1 bg-gray-200 rounded">←/→</kbd> - Navigate tabs when focused</li>
                <li><kbd class="px-2 py-1 bg-gray-200 rounded">Esc</kbd> - Close modal</li>
            </ul>
        </div>
    </div>';
    
    echo $content;
}

/**
 * Update current tab content
 */
function update_current($params = []) {
    $content = $params['content'] ?? 'Updated content';
    $timestamp = date('H:i:s');
    
    $updatedContent = '
    <div class="p-6">
        <h2 class="text-xl font-bold mb-4">Updated Content</h2>
        <p class="mb-4">' . htmlspecialchars($content) . '</p>
        <p class="text-sm text-gray-500">Last updated: ' . $timestamp . '</p>
        
        <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p class="text-green-800">✓ This tab content was updated without creating a new tab!</p>
        </div>
        
        <button 
            type="button"
            class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            hx-get="' . APP_ROOT . '/api/modal_tab_demo/demo_tab"
            hx-target="#modal_body"
            data-tab-title="New Demo Tab">
            Create New Tab
        </button>
    </div>';
    
    // Use the ModalTabManager to update the current tab
    \ModalTabManager::htmxResponse('Updated Tab', $updatedContent, '', true);
}

/**
 * Example of creating a form in a tab
 */
function form_tab($params = []) {
    $title = "Form Example";
    
    $content = '
    <div class="p-6">
        <h2 class="text-xl font-bold mb-4">Form in Tab</h2>
        
        <form hx-post="' . APP_ROOT . '/api/modal_tab_demo/submit_form" 
              hx-target="#modal_body"
              class="space-y-4">
            
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                <input type="text" id="name" name="name" required
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                <input type="email" id="email" name="email" required
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                <textarea id="message" name="message" rows="3"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
            </div>
            
            <div class="flex space-x-3">
                <button type="submit" 
                        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                    Submit Form
                </button>
                <button type="button" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                        @click="closeTab(currentTab)">
                    Cancel
                </button>
            </div>
        </form>
    </div>';
    
    echo $content;
}

/**
 * Handle form submission
 */
function submit_form($params = []) {
    $name = $params['name'] ?? '';
    $email = $params['email'] ?? '';
    $message = $params['message'] ?? '';
    
    $content = '
    <div class="p-6">
        <h2 class="text-xl font-bold mb-4">Form Submitted</h2>
        
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <h3 class="text-green-800 font-semibold">✓ Success!</h3>
            <p class="text-green-700">Your form has been submitted successfully.</p>
        </div>
        
        <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold mb-2">Submitted Data:</h4>
            <dl class="space-y-2">
                <div>
                    <dt class="font-medium">Name:</dt>
                    <dd>' . htmlspecialchars($name) . '</dd>
                </div>
                <div>
                    <dt class="font-medium">Email:</dt>
                    <dd>' . htmlspecialchars($email) . '</dd>
                </div>
                <div>
                    <dt class="font-medium">Message:</dt>
                    <dd>' . htmlspecialchars($message) . '</dd>
                </div>
            </dl>
        </div>
        
        <button 
            type="button"
            class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            hx-get="' . APP_ROOT . '/api/modal_tab_demo/form_tab"
            hx-target="#modal_body"
            data-tab-title="New Form">
            Create Another Form
        </button>
    </div>';
    
    \ModalTabManager::htmxResponse('Form Submitted', $content, '', true);
}

/**
 * Create multiple tabs demo
 */
function multiple_tabs($params = []) {
    // Create first tab
    \ModalTabState::addTab('Tab 1', '<div class="p-6"><h2 class="text-xl font-bold mb-4">First Tab</h2><p>This is the first tab content created server-side.</p></div>');

    // Create second tab
    \ModalTabState::addTab('Tab 2', '<div class="p-6"><h2 class="text-xl font-bold mb-4">Second Tab</h2><p>This is the second tab content created server-side.</p></div>');

    // Create third tab (this will be the active one)
    \ModalTabState::addTab('Tab 3', '<div class="p-6"><h2 class="text-xl font-bold mb-4">Third Tab</h2><p>This is the third tab content created server-side. All tabs were created in a single server request!</p></div>');

    // Return the complete modal
    echo '<div id="modal_container" hx-swap-oob="outerHTML">';
    echo \ModalTabState::renderModal();
    echo '</div>';

    // Trigger modal open
    header('HX-Trigger: openModal');
}
