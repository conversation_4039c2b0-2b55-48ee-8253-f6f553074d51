<?php

class ModalTabManager {

    private static $sessionKey = 'modal_tabs';

    /**
     * Get current tabs from session
     */
    public static function getTabs() {
        // Ensure session is started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION[self::$sessionKey])) {
            $_SESSION[self::$sessionKey] = [];
        }

        error_log("getTabs called, session status: " . session_status() . ", tabs: " . print_r($_SESSION[self::$sessionKey], true));

        return $_SESSION[self::$sessionKey];
    }

    /**
     * Save tabs to session
     */
    private static function saveTabs($tabs) {
        // Ensure session is started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $_SESSION[self::$sessionKey] = $tabs;
        error_log("saveTabs called, saved: " . print_r($tabs, true));
    }

    /**
     * Generate unique tab ID
     */
    private static function generateTabId() {
        return 'tab_' . uniqid();
    }

    /**
     * Add a new tab
     *
     * @param string $title Tab title
     * @param string $content Tab content (HTML)
     * @param string $url Optional URL for the tab
     * @param bool $pinned Whether tab is pinned
     * @return string Tab ID
     */
    public static function addTab($title, $content, $url = '', $pinned = false) {
        $tabs = self::getTabs();
        $tabId = self::generateTabId();

        $tabs[$tabId] = [
            'id' => $tabId,
            'title' => $title,
            'content' => $content,
            'url' => $url,
            'pinned' => $pinned,
            'active' => true // New tab becomes active
        ];

        // Set all other tabs to inactive
        foreach ($tabs as $id => &$tab) {
            if ($id !== $tabId) {
                $tab['active'] = false;
            }
        }

        self::saveTabs($tabs);
        return $tabId;
    }

    /**
     * Close a tab
     */
    public static function closeTab($tabId) {
        $tabs = self::getTabs();

        if (!isset($tabs[$tabId])) {
            return false;
        }

        $wasActive = $tabs[$tabId]['active'];
        unset($tabs[$tabId]);

        // If we closed the active tab, make another tab active
        if ($wasActive && !empty($tabs)) {
            $lastTab = array_pop($tabs);
            $lastTab['active'] = true;
            $tabs[$lastTab['id']] = $lastTab;
        }

        self::saveTabs($tabs);
        return true;
    }

    /**
     * Switch to a tab
     */
    public static function switchTab($tabId) {
        $tabs = self::getTabs();

        if (!isset($tabs[$tabId])) {
            return false;
        }

        // Set all tabs to inactive
        foreach ($tabs as &$tab) {
            $tab['active'] = false;
        }

        // Set target tab to active
        $tabs[$tabId]['active'] = true;

        self::saveTabs($tabs);
        return true;
    }

    /**
     * Toggle pin status of a tab
     */
    public static function togglePin($tabId) {
        $tabs = self::getTabs();

        if (!isset($tabs[$tabId])) {
            return false;
        }

        $tabs[$tabId]['pinned'] = !$tabs[$tabId]['pinned'];
        self::saveTabs($tabs);
        return $tabs[$tabId]['pinned'];
    }

    /**
     * Clear all tabs
     */
    public static function clearTabs() {
        $_SESSION[self::$sessionKey] = [];
    }

    /**
     * Render the complete tab interface
     *
     * @return string HTML for tabs and content
     */
    public static function renderTabs() {
        $tabs = self::getTabs();

        error_log("renderTabs called, tab count: " . count($tabs));
        error_log("Tabs data: " . print_r($tabs, true));

        if (empty($tabs)) {
            error_log("No tabs found, returning empty content");
            return '<div id="tab-contents" role="tabpanel"><!-- No tabs --></div>';
        }

        $html = '';

        // Render tab bar if we have tabs
        if (count($tabs) > 1) {
            $html .= '<div class="border-b border-gray-200 mb-4">';
            $html .= '<nav class="-mb-px flex space-x-8" aria-label="Tabs">';

            foreach ($tabs as $tab) {
                $activeClass = $tab['active']
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300';

                $html .= '<div class="flex items-center group">';

                // Tab button
                $html .= '<button role="tab" aria-controls="tab-contents" ';
                $html .= 'aria-selected="' . ($tab['active'] ? 'true' : 'false') . '" ';
                $html .= 'hx-get="' . APP_ROOT . '/api/modal/switch_tab" ';
                $html .= 'hx-vals=\'{"tab_id": "' . $tab['id'] . '"}\' ';
                $html .= 'hx-target="#modal_tabs_container" ';
                $html .= 'hx-swap="innerHTML" ';
                $html .= 'class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ' . $activeClass . '">';
                $html .= htmlspecialchars($tab['title']);
                $html .= '</button>';

                // Pin button
                $pinClass = $tab['pinned'] ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-600';
                $html .= '<button ';
                $html .= 'hx-get="' . APP_ROOT . '/api/modal/toggle_pin" ';
                $html .= 'hx-vals=\'{"tab_id": "' . $tab['id'] . '"}\' ';
                $html .= 'hx-target="#modal_tabs_container" ';
                $html .= 'hx-swap="innerHTML" ';
                $html .= 'class="ml-2 p-1 rounded hover:bg-gray-100 ' . $pinClass . '" ';
                $html .= 'title="' . ($tab['pinned'] ? 'Unpin tab' : 'Pin tab') . '">';

                if ($tab['pinned']) {
                    $html .= '<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">';
                    $html .= '<path fill-rule="evenodd" d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8zM9 5a2 2 0 00-2 2v6H6a1 1 0 100 2h8a1 1 0 100-2h-1V7a2 2 0 00-2-2H9zM7 8a1 1 0 012-2h2a1 1 0 110 2H9a1 1 0 01-2 0z" clip-rule="evenodd"/>';
                    $html .= '</svg>';
                } else {
                    $html .= '<svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>';
                    $html .= '</svg>';
                }
                $html .= '</button>';

                // Close button
                $html .= '<button ';
                $html .= 'hx-get="' . APP_ROOT . '/api/modal/close_tab" ';
                $html .= 'hx-vals=\'{"tab_id": "' . $tab['id'] . '"}\' ';
                $html .= 'hx-target="#modal_tabs_container" ';
                $html .= 'hx-swap="innerHTML" ';
                $html .= 'class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100" ';
                $html .= 'title="Close tab">';
                $html .= '<svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>';
                $html .= '</svg>';
                $html .= '</button>';

                $html .= '</div>';
            }

            $html .= '</nav>';
            $html .= '</div>';
        }

        // Render active tab content
        $activeTab = null;
        foreach ($tabs as $tab) {
            if ($tab['active']) {
                $activeTab = $tab;
                break;
            }
        }

        $html .= '<div id="tab-contents" role="tabpanel">';
        if ($activeTab) {
            $html .= $activeTab['content'];
        }
        $html .= '</div>';

        return $html;
    }

    /**
     * Handle opening new content - either create new tab or update existing
     *
     * @param string $title Tab title
     * @param string $content Tab content
     * @param string $url Optional URL
     * @return string Rendered tabs HTML
     */
    public static function openContent($title, $content, $url = '') {
        // Debug logging
        error_log("ModalTabManager::openContent called with title: {$title}");
        error_log("Content length: " . strlen($content));

        // Always create new tab
        $tabId = self::addTab($title, $content, $url);
        error_log("Created tab with ID: {$tabId}");

        $result = self::renderTabs();
        error_log("Rendered tabs HTML length: " . strlen($result));

        return $result;
    }
}
