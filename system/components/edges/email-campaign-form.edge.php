@props([
    'title' => 'Campaign Form',
    'campaign' => [],
    'is_edit' => false,
    'campaign_types' => []
])

<div class="space-y-6">
    <!-- Form Header -->
    <div class="border-b border-gray-200 pb-4">
        <h2 class="text-lg font-medium text-gray-900">{{ $title }}</h2>
        <p class="mt-1 text-sm text-gray-600">
            @if($is_edit)
                Update the campaign settings below.
            @else
                Create a new email campaign by filling out the form below.
            @endif
        </p>
    </div>

    <!-- Campaign Form -->
    <form hx-post="{{ APP_ROOT }}/api/email_campaigns/save_campaign"
          hx-target="#modal_tabs_container"
          hx-swap="innerHTML"
          class="space-y-6">
        
        @if($is_edit)
            <input type="hidden" name="id" value="{{ $campaign['id'] }}">
        @endif

        <!-- Basic Information -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div class="sm:col-span-2">
                <label for="name" class="block text-sm font-medium text-gray-700">Campaign Name</label>
                <input type="text"
                       name="name"
                       id="name"
                       value="{{ $campaign['name'] ?? '' }}"
                       required
                       placeholder="Enter campaign name"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
            </div>

            <div class="sm:col-span-2">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea name="description"
                          id="description"
                          rows="3"
                          placeholder="Describe the purpose of this campaign"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">{{ $campaign['description'] ?? '' }}</textarea>
            </div>

            <div>
                <label for="type" class="block text-sm font-medium text-gray-700">Campaign Type</label>
                <select name="type"
                        id="type"
                        required
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    @foreach($campaign_types as $value => $label)
                        <option value="{{ $value }}" {{ ($campaign['type'] ?? 'general') === $value ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div>
                <x-forms-select
                        name="status"
                        label="Status"
                        :options="[
                            'draft' => 'Draft',
                            'active' => 'Active',
                            'paused' => 'Paused',
                            'completed' => 'Completed',
                            'archived' => 'Archived'
                        ]";
                        :selected="$campaign['status'] ?? 'draft'"
                />

                {{-- <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                -<select name="status"
                         id="status"
                         required
                         class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                     @php
                         $status_options =
                     @endphp
                     @foreach($status_options as $value => $label)
                         <option value="{{ $value }}" {{ ($campaign['status'] ?? 'draft') === $value ? 'selected' : '' }}>
                             {{ $label }}
                         </option>
                     @endforeach
                 </select>
                 --}}
            </div>
        </div>

        <!-- Email Settings -->
        <div class="border-t border-gray-200 pt-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">Email Settings</h3>
            
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                    <label for="from_email" class="block text-sm font-medium text-gray-700">From Email</label>
                    <input type="email"
                           name="from_email"
                           id="from_email"
                           value="{{ $campaign['from_email'] ?? '<EMAIL>' }}"
                           required
                           placeholder="<EMAIL>"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                </div>

                <div>
                    <label for="from_name" class="block text-sm font-medium text-gray-700">From Name</label>
                    <input type="text"
                           name="from_name"
                           id="from_name"
                           value="{{ $campaign['from_name'] ?? 'TCS CAD & BIM Solutions Limited' }}"
                           required
                           placeholder="Your Company Name"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                </div>

                <div class="sm:col-span-2">
                    <label for="subject_template" class="block text-sm font-medium text-gray-700">Subject Template</label>
                    <input type="text"
                           name="subject_template"
                           id="subject_template"
                           value="{{ $campaign['subject_template'] ?? '' }}"
                           placeholder="Use double braces around placeholders for dynamic content"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">

                    <p class="mt-1 text-sm text-gray-500">
                        You can use placeholders like "&#123;&#123;column_name&#125;&#125;" - available placeholders are based on your selected data source.
                    </p>
                </div>
            </div>
        </div>

        <!-- Data Source Selection -->
        <div class="border-t border-gray-200 pt-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">Data Source</h3>
            <p class="text-sm text-gray-600 mb-4">Select the data source that will provide recipient data for this campaign.</p>

            <x-data-source-selector
                name="data_source_id"
                label="Campaign Data Source"
                :selected="$campaign['data_source_id'] ?? ''"
                :show_preview="true"
                :show_create_new="true"
                :required="true"
            />
        </div>

        <!-- Template Selection -->
        <div class="border-t border-gray-200 pt-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">Email Template</h3>

            <x-email-template-selector
                name="email_template"
                label="Select Email Template"
                :selected="$campaign['email_template'] ?? ''"
                :campaign_id="$campaign['id'] ?? null"
                :show_actions="true"
                :show_data_source="false"
                :selected_data_source="$campaign['data_source_id'] ?? ''"
            />

        </div>

        <!-- Advanced Settings (for subscription renewal type) -->
        @if(($campaign['type'] ?? '') === 'subscription_renewal')
        <div class="border-t border-gray-200 pt-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">Subscription Renewal Settings</h3>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Send Rules (Days before/after expiration)
                    </label>
                    @php
                        // Get existing send rules or use defaults
                        $existing_rules = [];
                        if (isset($campaign['send_rules'])) {
                            $rules_data = is_string($campaign['send_rules']) ? json_decode($campaign['send_rules'], true) : $campaign['send_rules'];
                            $existing_rules = $rules_data['days_before_expiry'] ?? ['90', '60', '30', '15'];
                        } else {
                            $existing_rules = ['90', '60', '30', '15'];
                        }
                        // Ensure we have at least 4 values
                        while (count($existing_rules) < 4) {
                            $existing_rules[] = '';
                        }
                    @endphp
                    <div class="flex space-x-2">
                        <input type="text"
                               name="send_rules[]"
                               value="{{ $existing_rules[0] ?? '90' }}"
                               placeholder="90"
                               class="block w-20 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <input type="text"
                               name="send_rules[]"
                               value="{{ $existing_rules[1] ?? '60' }}"
                               placeholder="60"
                               class="block w-20 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <input type="text"
                               name="send_rules[]"
                               value="{{ $existing_rules[2] ?? '30' }}"
                               placeholder="30"
                               class="block w-20 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <input type="text"
                               name="send_rules[]"
                               value="{{ $existing_rules[3] ?? '15' }}"
                               placeholder="15"
                               class="block w-20 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        Enter days before expiration to send reminders (e.g., 90, 60, 30, 15)
                    </p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Send Days
                    </label>
                    <div class="flex space-x-2">
                        @php
                            $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                            $day_abbr = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

                            // Get existing send days or use defaults (Mon-Fri)
                            $existing_days = [0, 1, 1, 1, 1, 1, 0]; // Default: Mon-Fri
                            if (isset($campaign['send_schedule'])) {
                                $schedule_data = is_string($campaign['send_schedule']) ? json_decode($campaign['send_schedule'], true) : $campaign['send_schedule'];
                                $existing_days = $schedule_data['send_days'] ?? $existing_days;
                            }
                        @endphp
                        @foreach($days as $index => $day)
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                                   name="send_days[]"
                                   value="{{ $index }}"
                                   {{ ($existing_days[$index] ?? 0) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <span class="ml-1 text-sm text-gray-700">{{ $day_abbr[$index] }}</span>
                        </label>
                        @endforeach
                    </div>
                </div>

                <div>
                    <label for="send_time" class="block text-sm font-medium text-gray-700">
                        Send Time (Hour of day, 0-23)
                    </label>
                    @php
                        // Get existing send time or use default
                        $existing_time = 9; // Default 9 AM
                        if (isset($campaign['send_schedule'])) {
                            $schedule_data = is_string($campaign['send_schedule']) ? json_decode($campaign['send_schedule'], true) : $campaign['send_schedule'];
                            $existing_time = $schedule_data['send_time'] ?? 9;
                        }
                    @endphp
                    <input type="number"
                           name="send_time"
                           id="send_time"
                           min="0"
                           max="23"
                           value="{{ $existing_time }}"
                           class="mt-1 block w-32 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                </div>
            </div>
        </div>
        @endif

        <!-- Form Actions -->
        <div class="border-t border-gray-200 pt-6">
            <div class="flex justify-end space-x-3">
                <x-forms-button
                    type="button"
                    label="Cancel"
                    variant="secondary"
                    @click="showModal = false"
                />

                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    {{ $is_edit ? 'Update Campaign' : 'Create Campaign' }}
                </button>
            </div>
        </div>
    </form>
</div>


