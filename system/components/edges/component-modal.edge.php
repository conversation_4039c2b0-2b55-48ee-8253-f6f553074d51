@props([
    'title' => 'model',
    'description' => 'A model',
    'content' => [], // An array of [('header'|'body'|'footer') => 'content']
    'title' => '',
])

<!-- Modal Container -->
<div id="modal_container" x-show="showModal"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto"
     style="display: none;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 transition-opacity">
            <div class="absolute inset-0 bg-gray-500 opacity-75" @click="showModal = false"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full sm:p-6">

            <!-- Global Close Button -->
            <div class="absolute top-0 right-0 pt-4 pr-4">
                <button type="button"
                        hx-get="{{ APP_ROOT }}/api/modal/close"
                        hx-target="#modal_container"
                        hx-swap="outerHTML"
                        class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="sr-only">Close</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Tab Container - Server-rendered -->
            <div id="modal_tabs_container"
                 hx-target="#tab-contents"
                 role="tablist"
                 hx-on:htmx:after-on-load="
                     let currentTab = document.querySelector('[aria-selected=true]');
                     if (currentTab) {
                         currentTab.setAttribute('aria-selected', 'false');
                         currentTab.classList.remove('border-indigo-500', 'text-indigo-600');
                         currentTab.classList.add('border-transparent', 'text-gray-500');
                     }
                     let newTab = event.target;
                     newTab.setAttribute('aria-selected', 'true');
                     newTab.classList.remove('border-transparent', 'text-gray-500');
                     newTab.classList.add('border-indigo-500', 'text-indigo-600');
                 ">
                <!-- Initial empty state - will be populated by server -->
                <div id="tab-contents" role="tabpanel">
                    <!-- Tab content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>


