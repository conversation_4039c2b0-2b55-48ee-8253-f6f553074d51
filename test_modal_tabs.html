<!DOCTYPE html>
<html>
<head>
    <title>Modal Tab Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/htmx.org@2.0.3"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        var APP_ROOT = '/baffletrain/autocadlt/autobooks';
    </script>
    <script src="resources/components/js/modal-tab-handler.js"></script>
</head>
<body class="h-full bg-gray-100" x-data="{ showModal: false }" x-bind:class="showModal && 'overflow-hidden'">

<div class="p-8">
    <h1 class="text-2xl font-bold mb-6">Modal Tab Test</h1>
    
    <div class="space-y-4">
        <button 
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            hx-get="/baffletrain/autocadlt/autobooks/api/modal/test_tab"
            hx-target="#modal_tabs_container"
            hx-swap="innerHTML"
            data-tab-title="Test Tab 1"
            onclick="this.closest('[x-data]').__x.$data.showModal = true; console.log('Opening modal');">
            Open Test Tab 1
        </button>
        
        <button 
            type="button"
            class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            hx-get="/baffletrain/autocadlt/autobooks/api/modal/test_tab"
            hx-vals='{"title": "Test Tab 2"}'
            hx-target="#modal_tabs_container"
            hx-swap="innerHTML"
            data-tab-title="Test Tab 2"
            onclick="this.closest('[x-data]').__x.$data.showModal = true; console.log('Opening modal');">
            Open Test Tab 2
        </button>
        
        <button 
            type="button"
            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            onclick="this.closest('[x-data]').__x.$data.showModal = false; console.log('Closing modal');">
            Close Modal
        </button>
    </div>
    
    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h3 class="font-semibold mb-2">Debug Info:</h3>
        <p>Modal State: <span x-text="showModal ? 'Open' : 'Closed'"></span></p>
        <p>Check browser console for debug messages</p>
    </div>
</div>

<!-- Modal Container -->
<div id="modal_container" x-show="showModal"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto"
     style="display: none;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 transition-opacity">
            <div class="absolute inset-0 bg-gray-500 opacity-75" @click="showModal = false"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full sm:p-6">
            
            <!-- Global Close Button -->
            <div class="absolute top-0 right-0 pt-4 pr-4">
                <button type="button"
                        @click="showModal = false"
                        class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="sr-only">Close</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <!-- Tab Container - Server-rendered -->
            <div id="modal_tabs_container">
                <div id="tab-contents" role="tabpanel">
                    <!-- Tab content will be loaded here -->
                    <div class="p-6 text-gray-500">Click a button above to load tab content</div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
